// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Arabic (`ar`).
class AppLocalizationsAr extends AppLocalizations {
  AppLocalizationsAr([String locale = 'ar']) : super(locale);

  @override
  String get done => 'تم';

  @override
  String get loading => 'جاري التحميل...';

  @override
  String get messageHasBeenDeleted => 'تم حذف الرسالة';

  @override
  String get mute => 'كتم';

  @override
  String get cancel => 'إلغاء';

  @override
  String get typing => 'يكتب...';

  @override
  String get ok => 'موافق';

  @override
  String get recording => 'جاري التسجيل...';

  @override
  String get connecting => 'جاري الاتصال...';

  @override
  String get deleteYouCopy => 'حذف نسختك';

  @override
  String get unMute => 'إلغاء الكتم';

  @override
  String get delete => 'حذف';

  @override
  String get report => 'تقرير';

  @override
  String get leaveGroup => 'مغادرة المجموعة';

  @override
  String get areYouSureToPermitYourCopyThisActionCantUndo =>
      'هل أنت متأكد من السماح بنسختك؟ لا يمكن التراجع عن هذا الإجراء';

  @override
  String get areYouSureToLeaveThisGroupThisActionCantUndo =>
      'هل أنت متأكد من مغادرة هذه المجموعة؟ لا يمكن التراجع عن هذا الإجراء';

  @override
  String get leaveGroupAndDeleteYourMessageCopy =>
      'مغادرة المجموعة وحذف نسخة رسالتك';

  @override
  String get vMessageInfoTrans => 'معلومات الرسالة';

  @override
  String get updateTitleTo => 'تحديث العنوان إلى';

  @override
  String get updateImage => 'تحديث الصورة';

  @override
  String get joinedBy => 'انضم بواسطة';

  @override
  String get promotedToAdminBy => 'تم ترقيته إلى مشرف بواسطة';

  @override
  String get dismissedToMemberBy => 'تم تنزيله إلى عضو بواسطة';

  @override
  String get leftTheGroup => 'غادر المجموعة';

  @override
  String get you => 'أنت';

  @override
  String get kickedBy => 'تم طرده بواسطة';

  @override
  String get groupCreatedBy => 'تم إنشاء المجموعة بواسطة';

  @override
  String get addedYouToNewBroadcast => 'تمت إضافتك إلى بث جديد';

  @override
  String get download => 'تحميل';

  @override
  String get copy => 'نسخ';

  @override
  String get info => 'معلومات';

  @override
  String get share => 'مشاركة';

  @override
  String get forward => 'إعادة توجيه';

  @override
  String get reply => 'الرد';

  @override
  String get reacted => 'Reacted';

  @override
  String get replied => 'Replied';

  @override
  String get deleteFromAll => 'حذف من الجميع';

  @override
  String get deleteFromMe => 'حذف مني';

  @override
  String get downloading => 'جاري التحميل...';

  @override
  String get fileHasBeenSavedTo => 'تم حفظ الملف في';

  @override
  String get online => 'متصل';

  @override
  String get youDontHaveAccess => 'ليس لديك صلاحية الوصول';

  @override
  String get replyToYourSelf => 'الرد على نفسك';

  @override
  String get repliedToYourSelf => 'تم الرد على نفسك';

  @override
  String get audioCall => 'مكالمة صوتية';

  @override
  String get ring => 'رنين';

  @override
  String get canceled => 'تم الإلغاء';

  @override
  String get timeout => 'انتهاء الوقت';

  @override
  String get rejected => 'مرفوض';

  @override
  String get finished => 'انتهى';

  @override
  String get inCall => 'قيد المكالمة';

  @override
  String get sessionEnd => 'انتهاء الجلسة';

  @override
  String get yesterday => 'أمس';

  @override
  String get today => 'اليوم';

  @override
  String get textFieldHint => 'اكتب رسالة...';

  @override
  String get files => 'ملفات';

  @override
  String get location => 'الموقع';

  @override
  String get shareMediaAndLocation => 'مشاركة الوسائط والموقع';

  @override
  String get thereIsVideoSizeBiggerThanAllowedSize =>
      'هناك فيديو بحجم أكبر من الحجم المسموح به';

  @override
  String get thereIsFileHasSizeBiggerThanAllowedSize =>
      'هناك ملف بحجم أكبر من الحجم المسموح به';

  @override
  String get makeCall => 'إجراء مكالمة';

  @override
  String get areYouWantToMakeVideoCall => 'هل ترغب في إجراء مكالمة فيديو؟';

  @override
  String get areYouWantToMakeVoiceCall => 'هل ترغب في إجراء مكالمة صوتية؟';

  @override
  String get vMessagesInfoTrans => 'معلومات الرسائل';

  @override
  String get star => 'تثبيت';

  @override
  String get minutes => 'دقائق';

  @override
  String get sendMessage => 'إرسال رسالة';

  @override
  String get deleteUser => 'حذف المستخدم';

  @override
  String get actions => 'إجراءات';

  @override
  String get youAreAboutToDeleteThisUserFromYourList =>
      'أنت على وشك حذف هذا المستخدم من قائمتك';

  @override
  String get updateBroadcastTitle => 'تحديث عنوان البث';

  @override
  String get usersAddedSuccessfully => 'تمت إضافة المستخدمين بنجاح';

  @override
  String get broadcastSettings => 'إعدادات البث';

  @override
  String get addParticipants => 'إضافة مشاركين';

  @override
  String get broadcastParticipants => 'مشاركو البث';

  @override
  String get updateGroupDescription => 'تحديث وصف المجموعة';

  @override
  String get updateGroupTitle => 'تحديث عنوان المجموعة';

  @override
  String get groupSettings => 'إعدادات المجموعة';

  @override
  String get description => 'الوصف';

  @override
  String get muteNotifications => 'كتم الإشعارات';

  @override
  String get groupParticipants => 'مشاركو المجموعة';

  @override
  String get blockUser => 'حظر المستخدم';

  @override
  String get areYouSureToBlock => 'هل أنت متأكد من حظر';

  @override
  String get userPage => 'صفحة المستخدم';

  @override
  String get starMessage => 'تثبيت الرسالة';

  @override
  String get showMedia => 'عرض الوسائط';

  @override
  String get reportUser => 'الإبلاغ عن المستخدم';

  @override
  String get groupName => 'اسم المجموعة';

  @override
  String get changeSubject => 'تغيير الموضوع';

  @override
  String get titleIsRequired => 'العنوان مطلوب';

  @override
  String get createBroadcast => 'إنشاء بث';

  @override
  String get broadcastName => 'اسم البث';

  @override
  String get createGroup => 'إنشاء مجموعة';

  @override
  String get forgetPassword => 'نسيت كلمة المرور';

  @override
  String get globalSearch => 'البحث العالمي';

  @override
  String get dismissesToMember => 'تنزيل إلى عضو';

  @override
  String get setToAdmin => 'تعيين كمشرف';

  @override
  String get kickMember => 'طرد العضو';

  @override
  String get youAreAboutToDismissesToMember => 'أنت على وشك تنزيل إلى عضو';

  @override
  String get youAreAboutToKick => 'أنت على وشك الطرد';

  @override
  String get groupMembers => 'أعضاء المجموعة';

  @override
  String get tapForPhoto => 'انقر للحصول على صورة';

  @override
  String get weHighRecommendToDownloadThisUpdate =>
      'نوصي بشدة بتنزيل هذا التحديث';

  @override
  String get newGroup => 'مجموعة جديدة';

  @override
  String get newBroadcast => 'بث جديد';

  @override
  String get starredMessage => 'الرسائل المثبتة';

  @override
  String get settings => 'الإعدادات';

  @override
  String get chats => 'الدردشات';

  @override
  String get recentUpdates => 'التحديثات الأخيرة';

  @override
  String get startChat => 'بدء الدردشة';

  @override
  String get newUpdateIsAvailable => 'تحديث جديد متاح الآن';

  @override
  String get emailNotValid => 'البريد الإلكتروني غير صالح';

  @override
  String get passwordMustHaveValue => 'يجب أن تحتوي كلمة المرور على قيمة';

  @override
  String get error => 'خطأ';

  @override
  String get password => 'كلمة المرور';

  @override
  String get login => 'تسجيل الدخول';

  @override
  String get needNewAccount => 'بحاجة إلى حساب جديد؟';

  @override
  String get register => 'التسجيل';

  @override
  String get nameMustHaveValue => 'يجب أن يحتوي الاسم على قيمة';

  @override
  String get passwordNotMatch => 'كلمة المرور غير متطابقة';

  @override
  String get name => 'الاسم';

  @override
  String get email => 'البريد الإلكتروني';

  @override
  String get confirmPassword => 'تأكيد كلمة المرور';

  @override
  String get alreadyHaveAnAccount => 'لديك بالفعل حساب؟';

  @override
  String get logOut => 'تسجيل الخروج';

  @override
  String get back => 'العودة';

  @override
  String get sendCodeToMyEmail => 'إرسال الرمز إلى بريدي الإلكتروني';

  @override
  String get invalidLoginData => 'بيانات تسجيل الدخول غير صالحة';

  @override
  String get userEmailNotFound => 'عنوان البريد الإلكتروني للمستخدم غير موجود';

  @override
  String get yourAccountBlocked => 'تم حظر حسابك';

  @override
  String get yourAccountDeleted => 'تم حذف حسابك';

  @override
  String get userAlreadyRegister => 'المستخدم مسجل بالفعل';

  @override
  String get codeHasBeenExpired => 'انتهى صلاحية الرمز';

  @override
  String get invalidCode => 'رمز غير صالح';

  @override
  String get whileAuthCanFindYou => 'أثناء المصادقة لا يمكن العثور عليك';

  @override
  String get userRegisterStatusNotAcceptedYet =>
      'حالة تسجيل المستخدم لم تتم قبولها بعد';

  @override
  String get deviceHasBeenLogoutFromAllDevices =>
      'تم تسجيل الخروج من جهازك من جميع الأجهزة';

  @override
  String get userDeviceSessionEndDeviceDeleted =>
      'جلسة جهاز المستخدم انتهت وتم حذف الجهاز';

  @override
  String get noCodeHasBeenSendToYouToVerifyYourEmail =>
      'لم يتم إرسال أي رمز للتحقق من بريدك الإلكتروني';

  @override
  String get roomAlreadyInCall => 'الغرفة بالفعل في مكالمة';

  @override
  String get peerUserInCallNow => 'المستخدم في مكالمة الآن';

  @override
  String get callNotAllowed => 'غير مسموح بالمكالمة';

  @override
  String get peerUserDeviceOffline => 'الجهاز الخاص بالمستخدم غير متصل';

  @override
  String get emailMustBeValid => 'يجب أن يكون البريد الإلكتروني صالح';

  @override
  String get wait2MinutesToSendMail => 'انتظر دقيقتين لإرسال البريد';

  @override
  String get codeMustEqualToSixNumbers =>
      'يجب أن يكون الرمز مكونًا من ستة أرقام';

  @override
  String get newPasswordMustHaveValue =>
      'يجب أن تحتوي كلمة المرور الجديدة على قيمة';

  @override
  String get confirmPasswordMustHaveValue =>
      'يجب أن تحتوي تأكيد كلمة المرور على قيمة';

  @override
  String get congregationsYourAccountHasBeenAccepted =>
      'Congregations your account has been accepted';

  @override
  String get yourAccountIsUnderReview => 'حسابك قيد المراجعة';

  @override
  String get waitingList => 'قائمة الانتظار';

  @override
  String get welcome => 'مرحبًا';

  @override
  String get retry => 'إعادة المحاولة';

  @override
  String get deleteMember => 'حذف العضو';

  @override
  String get profile => 'الملف الشخصي';

  @override
  String get broadcastInfo => 'معلومات البث';

  @override
  String get updateTitle => 'تحديث العنوان';

  @override
  String get members => 'الأعضاء';

  @override
  String get addMembers => 'إضافة أعضاء';

  @override
  String get success => 'نجاح';

  @override
  String get media => 'وسائط';

  @override
  String get docs => 'وثائق';

  @override
  String get links => 'روابط';

  @override
  String get soon => 'قريبًا';

  @override
  String get unStar => 'إلغاء التمييز';

  @override
  String get updateGroupDescriptionWillUpdateAllGroupMembers =>
      'سيتم تحديث وصف المجموعة وجميع أعضاء المجموعة';

  @override
  String get updateNickname => 'تحديث الاسم المستعار';

  @override
  String get groupInfo => 'معلومات المجموعة';

  @override
  String get youNotParticipantInThisGroup => 'أنت لست مشاركًا في هذه المجموعة';

  @override
  String get search => 'بحث';

  @override
  String get mediaLinksAndDocs => 'وسائط، روابط ووثائق';

  @override
  String get starredMessages => 'رسائل مميزة';

  @override
  String get nickname => 'الاسم المستعار';

  @override
  String get none => 'بلا';

  @override
  String get yes => 'نعم';

  @override
  String get no => 'لا';

  @override
  String get exitGroup => 'الخروج من المجموعة';

  @override
  String get clickToAddGroupDescription => 'انقر لإضافة وصف المجموعة';

  @override
  String get unBlockUser => 'إلغاء حظر المستخدم';

  @override
  String get areYouSureToUnBlock =>
      'هل أنت متأكد من رغبتك في إلغاء حظر المستخدم؟';

  @override
  String get contactInfo => 'معلومات الاتصال';

  @override
  String get audio => 'صوت';

  @override
  String get video => 'فيديو';

  @override
  String get hiIamUse => 'مرحبًا، أنا استخدم';

  @override
  String get on => 'تشغيل';

  @override
  String get off => 'إيقاف';

  @override
  String get unBlock => 'إلغاء الحظر';

  @override
  String get block => 'حظر';

  @override
  String get chooseAtLestOneMember => 'Choose at lest one member';

  @override
  String get close => 'إغلاق';

  @override
  String get next => 'التالي';

  @override
  String get appMembers => 'أعضاء التطبيق';

  @override
  String get create => 'إنشاء';

  @override
  String get upgradeToAdmin => 'الترقية إلى مسؤول';

  @override
  String get update => 'تحديث';

  @override
  String get deleteChat => 'حذف الدردشة';

  @override
  String get clearChat => 'مسح الدردشة';

  @override
  String get showHistory => 'عرض التاريخ';

  @override
  String get groupIcon => 'أيقونة المجموعة';

  @override
  String get tapToSelectAnIcon => 'اضغط لاختيار أيقونة';

  @override
  String get groupDescription => 'وصف المجموعة';

  @override
  String get more => 'المزيد';

  @override
  String get messageInfo => 'معلومات الرسالة';

  @override
  String get successfullyDownloadedIn => 'تم التنزيل بنجاح في';

  @override
  String get delivered => 'تم التسليم';

  @override
  String get read => 'تمت القراءة';

  @override
  String get orLoginWith => 'أو تسجيل الدخول باستخدام';

  @override
  String get resetPassword => 'إعادة تعيين كلمة المرور';

  @override
  String get otpCode => 'رمز OTP';

  @override
  String get newPassword => 'كلمة المرور الجديدة';

  @override
  String get areYouSure => 'هل أنت متأكد؟';

  @override
  String get broadcastMembers => 'أعضاء البث';

  @override
  String get phone => 'الهاتف';

  @override
  String get users => 'المستخدمين';

  @override
  String get calls => 'المكالمات';

  @override
  String get yourAreAboutToLogoutFromThisAccount =>
      'أنت على وشك تسجيل الخروج من هذا الحساب';

  @override
  String get noUpdatesAvailableNow => 'لا تتوفر تحديثات الآن';

  @override
  String get dataPrivacy => 'خصوصية البيانات';

  @override
  String get allDataHasBeenBackupYouDontNeedToManageSaveTheDataByYourself =>
      'تمت محفظة جميع البيانات، لا تحتاج إلى إدارة حفظ البيانات بنفسك! إذا قمت بتسجيل الخروج وتسجيل الدخول مرة أخرى، ستظهر جميع الدردشات كما هي في نسخة الويب';

  @override
  String get account => 'الحساب';

  @override
  String get linkedDevices => 'الأجهزة المرتبطة';

  @override
  String get storageAndData => 'التخزين والبيانات';

  @override
  String get tellAFriend => 'أخبر صديقًا';

  @override
  String get help => 'مساعدة';

  @override
  String get blockedUsers => 'المستخدمين المحظورين';

  @override
  String get inAppAlerts => 'تنبيهات في التطبيق';

  @override
  String get language => 'اللغة';

  @override
  String get adminNotification => 'إشعار المسؤول';

  @override
  String get checkForUpdates => 'التحقق من التحديثات';

  @override
  String get linkByQrCode => 'الربط عبر رمز الاستجابة السريعة';

  @override
  String get deviceStatus => 'حالة الجهاز';

  @override
  String get desktopAndOtherDevices => 'سطح المكتب والأجهزة الأخرى';

  @override
  String get linkADeviceSoon => 'ربط جهاز (قريبًا)';

  @override
  String get lastActiveFrom => 'آخر نشاط من';

  @override
  String get tapADeviceToEditOrLogOut =>
      'اضغط على جهاز للتعديل أو تسجيل الخروج.';

  @override
  String get contactUs => 'اتصل بنا';

  @override
  String get supportChatSoon => 'الدعم عبر الدردشة (قريبًا)';

  @override
  String get updateYourName => 'تحديث اسمك';

  @override
  String get updateYourBio => 'تحديث نبذة عنك';

  @override
  String get edit => 'تعديل';

  @override
  String get about => 'حول';

  @override
  String get oldPassword => 'كلمة المرور القديمة';

  @override
  String get deleteMyAccount => 'حذف حسابي';

  @override
  String get passwordHasBeenChanged => 'تم تغيير كلمة المرور';

  @override
  String get logoutFromAllDevices => 'الخروج من جميع الأجهزة؟';

  @override
  String get updateYourPassword => 'تحديث كلمة المرور الخاصة بك';

  @override
  String get enterNameAndAddOptionalProfilePicture =>
      'أدخل اسمك وأضف صورة شخصية اختيارية';

  @override
  String get privacyPolicy => 'سياسة الخصوصية';

  @override
  String get chat => 'الدردشة';

  @override
  String get send => 'إرسال';

  @override
  String get reportHasBeenSubmitted => 'تم تقديم التقرير الخاص بك';

  @override
  String get offline => 'غير متصل';

  @override
  String get harassmentOrBullyingDescription =>
      'التحرش أو الابتزاز: تتيح هذه الخيارات للمستخدمين الإبلاغ عن الأفراد الذين يستهدفونهم أو الآخرين برسائل مضايقة أو تهديدات أو أشكال أخرى من التنمر.';

  @override
  String get spamOrScamDescription =>
      'البريد المزعج أو الاحتيال: يمكن للمستخدمين الإبلاغ عن حسابات ترسل رسائل بريد مزعجة أو إعلانات غير مرغوب فيها أو تحاول خداع الآخرين.';

  @override
  String get areYouSureToReportUserToAdmin =>
      'هل أنت متأكد من رغبتك في تقديم تقرير عن هذا المستخدم إلى المسؤول؟';

  @override
  String get groupWith => 'المجموعة مع';

  @override
  String get inappropriateContentDescription =>
      'المحتوى غير المناسب: يمكن للمستخدمين اختيار هذا الخيار للإبلاغ عن أي مواد جنسية صريحة أو خطب الكراهية أو أي محتوى ينتهك معايير المجتمع.';

  @override
  String get otherCategoryDescription =>
      'أخرى: يمكن استخدام هذا الفئة الشاملة للانتهاكات التي لا تناسب بسهولة الفئات السابقة. قد يكون من المفيد تضمين مربع نصي يمكن للمستخدمين من خلاله تقديم تفاصيل إضافية.';

  @override
  String get explainWhatHappens => 'شرح ما يحدث هنا';

  @override
  String get loginAgain => 'تسجيل الدخول مرة أخرى!';

  @override
  String get yourSessionIsEndedPleaseLoginAgain =>
      'انتهت جلستك، يرجى تسجيل الدخول مرة أخرى!';

  @override
  String get aboutToBlockUserWithConsequences =>
      'أنت على وشك حظر هذا المستخدم. لن تتمكن من إرسال رسائل له ولا إضافته إلى المجموعات أو البث!';

  @override
  String
      get youAreAboutToDeleteYourAccountYourAccountWillNotAppearAgainInUsersList =>
          'أنت على وشك حذف حسابك، لن يظهر حسابك مرة أخرى في قائمة المستخدمين';

  @override
  String get admin => 'مسوال';

  @override
  String get member => 'عضو';

  @override
  String get creator => 'المنشاء';

  @override
  String get currentDevice => 'الجهاز الحالي';

  @override
  String get visits => 'زيارات';

  @override
  String get chooseRoom => 'اختار المحادثات';

  @override
  String get deleteThisDeviceDesc =>
      'مسح الجهاز سوف يخرج اليوزر من التطبيق حالا';

  @override
  String get youAreAboutToUpgradeToAdmin => 'أنت على وشك الترقية إلى مشرف';

  @override
  String get microphonePermissionMustBeAccepted =>
      'يجب الموافقه علي ازن الميكروفون';

  @override
  String get microphoneAndCameraPermissionMustBeAccepted =>
      'يجب الموافقه علي ازن الميكروفون والكاميرا';

  @override
  String get loginNowAllowedNowPleaseTryAgainLater =>
      'نأسف، حالياً غير مسموح بتسجيل الدخول. الرجاء المحاولة مرة أخرى لاحقًا.';

  @override
  String get dashboard => 'لوحة القيادة';

  @override
  String get notification => 'الإشعار';

  @override
  String get total => 'الإجمالي';

  @override
  String get blocked => 'تم حظره';

  @override
  String get deleted => 'تم حذفه';

  @override
  String get accepted => 'تم قبوله';

  @override
  String get notAccepted => 'غير مقبول';

  @override
  String get web => 'ويب';

  @override
  String get android => 'أندرويد';

  @override
  String get macOs => 'macOS';

  @override
  String get windows => 'ويندوز';

  @override
  String get other => 'آخر';

  @override
  String get totalVisits => 'إجمالي الزيارات';

  @override
  String get totalMessages => 'إجمالي الرسائل';

  @override
  String get textMessages => 'رسائل نصية';

  @override
  String get imageMessages => 'رسائل صور';

  @override
  String get videoMessages => 'رسائل فيديو';

  @override
  String get voiceMessages => 'رسائل صوتية';

  @override
  String get fileMessages => 'رسائل ملفات';

  @override
  String get infoMessages => 'رسائل معلومات';

  @override
  String get voiceCallMessages => 'رسائل مكالمات صوتية';

  @override
  String get videoCallMessages => 'رسائل مكالمات فيديو';

  @override
  String get locationMessages => 'رسائل الموقع';

  @override
  String get directChat => 'دردشة مباشرة';

  @override
  String get group => 'مجموعة';

  @override
  String get broadcast => 'بث';

  @override
  String get messageCounter => 'عداد الرسائل';

  @override
  String get roomCounter => 'عداد الغرف';

  @override
  String get countries => 'الدول';

  @override
  String get devices => 'الأجهزة';

  @override
  String get notificationTitle => 'عنوان الإشعار';

  @override
  String get notificationDescription => 'وصف الإشعار';

  @override
  String get notificationsPage => 'صفحة الإشعارات';

  @override
  String get updateFeedBackEmail => 'تحديث عنوان البريد الإلكتروني للملاحظات';

  @override
  String get setMaxMessageForwardAndShare =>
      'تعيين حد أقصى لإعادة توجيه ومشاركة الرسائل';

  @override
  String get setNewPrivacyPolicyUrl => 'تعيين رابط سياسة الخصوصية الجديد';

  @override
  String get forgetPasswordExpireTime => 'وقت انتهاء صلاحية نسيان كلمة المرور';

  @override
  String get callTimeoutInSeconds => 'مهلة المكالمة في ثوانٍ';

  @override
  String get setMaxGroupMembers => 'تعيين حد أقصى لأعضاء المجموعة';

  @override
  String get setMaxBroadcastMembers => 'تعيين حد أقصى لأعضاء البث';

  @override
  String get allowCalls => 'السماح بالمكالمات';

  @override
  String get ifThisOptionEnabledTheVideoAndVoiceCallWillBeAllowed =>
      'إذا تم تمكين هذا الخيار، سيتم السماح بالمكالمات الصوتية والفيديو';

  @override
  String get allowAds => 'السماح بالإعلانات';

  @override
  String get allowMobileLogin => 'السماح بتسجيل الدخول عبر الهاتف المحمول';

  @override
  String get allowWebLogin => 'السماح بتسجيل الدخول عبر الويب';

  @override
  String get messages => 'الرسائل';

  @override
  String get appleStoreAppUrl => 'رابط تطبيق متجر آبل';

  @override
  String get googlePlayAppUrl => 'رابط تطبيق متجر جوجل بلاي';

  @override
  String get privacyUrl => 'رابط الخصوصية';

  @override
  String get feedBackEmail => 'بريد الملاحظات';

  @override
  String get ifThisOptionDisabledTheSendChatFilesImageVideosAndLocationWillBeBlocked =>
      'إذا تم تعطيل هذا الخيار، سيتم حظر إرسال ملفات الدردشة والصور ومقاطع الفيديو والموقع';

  @override
  String get allowSendMedia => 'السماح بإرسال الوسائط';

  @override
  String get ifThisOptionDisabledTheCreateChatBroadcastWillBeBlocked =>
      'إذا تم تعطيل هذا الخيار، سيتم حظر إنشاء البث';

  @override
  String get allowCreateBroadcast => 'السماح بإنشاء البث';

  @override
  String get ifThisOptionDisabledTheCreateChatGroupsWillBeBlocked =>
      'إذا تم تعطيل هذا الخيار، سيتم حظر إنشاء المجموعات';

  @override
  String get allowCreateGroups => 'السماح بإنشاء المجموعات';

  @override
  String get ifThisOptionDisabledTheDesktopLoginOrRegisterWindowsMacWillBeBlocked =>
      'إذا تم تعطيل هذا الخيار، سيتم حظر تسجيل الدخول أو التسجيل عبر سطح المكتب (ويندوز وmacOS)';

  @override
  String get allowDesktopLogin => 'السماح بتسجيل الدخول عبر سطح المكتب';

  @override
  String get ifThisOptionDisabledTheWebLoginOrRegisterWillBeBlocked =>
      'إذا تم تعطيل هذا الخيار، سيتم حظر تسجيل الدخول أو التسجيل عبر الويب';

  @override
  String
      get ifThisOptionDisabledTheMobileLoginOrRegisterWillBeBlockedOnAndroidIosOnly =>
          'إذا تم تمكين هذا الخيار، سيظهر إعلان Google Ads في الدردشات';

  @override
  String get ifThisOptionEnabledTheGoogleAdsBannerWillAppearInChats =>
      'السماح بتسجيل الدخول عبر الجوال أو التسجيل (على Android وiOS فقط)';

  @override
  String get userProfile => 'ملف المستخدم';

  @override
  String get userInfo => 'معلومات المستخدم';

  @override
  String get fullName => 'الاسم الكامل';

  @override
  String get bio => 'السيرة الذاتية';

  @override
  String get noBio => 'لا توجد سيرة ذاتية';

  @override
  String get verifiedAt => 'تم التحقق في';

  @override
  String get country => 'البلد';

  @override
  String get registerStatus => 'حالة التسجيل';

  @override
  String get registerMethod => 'طريقة التسجيل';

  @override
  String get banTo => 'الحظر حتى';

  @override
  String get deletedAt => 'تم الحذف في';

  @override
  String get createdAt => 'تم الإنشاء في';

  @override
  String get updatedAt => 'تم التحديث في';

  @override
  String get reports => 'التقارير';

  @override
  String get clickToSeeAllUserDevicesDetails =>
      'انقر لعرض تفاصيل جميع أجهزة المستخدم';

  @override
  String get allDeletedMessages => 'جميع الرسائل المحذوفة';

  @override
  String get voiceCallMessage => 'رسالة مكالمة صوتية';

  @override
  String get totalRooms => 'إجمالي الغرف';

  @override
  String get directRooms => 'غرف مباشرة';

  @override
  String get userAction => 'إجراء المستخدم';

  @override
  String get status => 'الحالة';

  @override
  String get joinedAt => 'انضم في';

  @override
  String get saveLogin => 'حفظ تسجيل الدخول';

  @override
  String get passwordIsRequired => 'كلمة المرور مطلوبة';

  @override
  String get verified => 'تم التحقق';

  @override
  String get pending => 'قيد الانتظار';

  @override
  String get ios => 'iOS';

  @override
  String get descriptionIsRequired => 'الوصف مطلوب';

  @override
  String get seconds => 'ثواني';

  @override
  String get clickToSeeAllUserInformations => 'انقر لعرض جميع معلومات المستخدم';

  @override
  String get clickToSeeAllUserCountries => 'انقر لعرض جميع دول المستخدم';

  @override
  String get clickToSeeAllUserMessagesDetails =>
      'انقر لعرض تفاصيل جميع رسائل المستخدم';

  @override
  String get clickToSeeAllUserRoomsDetails =>
      'انقر لعرض تفاصيل جميع غرف المستخدم';

  @override
  String get clickToSeeAllUserReports => 'انقر لعرض جميع التقارير المستخدم';

  @override
  String get banAt => 'تم الحظر في';

  @override
  String get nowYouLoginAsReadOnlyAdminAllEditYouDoneWillNotAppliedDueToThisIsTestVersion =>
      'الآن تسجيل الدخول كمسؤول قائم على القراءة فقط. لن يتم تطبيق أي تعديلات تقوم به نظرًا لأن هذا هو إصدار الاختبار.';

  @override
  String get createStory => 'إنشاء قصة';

  @override
  String get writeACaption => 'اكتب تعليقا ...';

  @override
  String get storyCreatedSuccessfully => 'تم إنشاء القصة بنجاح';

  @override
  String get stories => 'قصص';

  @override
  String get clear => 'مسح';

  @override
  String get clearCallsConfirm => 'تأكيد مسح المكالمات';

  @override
  String get chooseHowAutomaticDownloadWorks =>
      'اختر كيفية عمل التنزيل التلقائي';

  @override
  String get whenUsingMobileData => 'عند استخدام بيانات الجوال';

  @override
  String get whenUsingWifi => 'عند استخدام Wi-Fi';

  @override
  String get image => 'صورة';

  @override
  String get myPrivacy => 'خصوصيتي';

  @override
  String get createTextStory => 'إنشاء قصة نصية';

  @override
  String get createMediaStory => 'إنشاء قصة وسائط';

  @override
  String get camera => 'كاميرا';

  @override
  String get gallery => 'معرض';

  @override
  String get recentUpdate => 'تحديث حديث';

  @override
  String get viewedUpdates => 'Viewed updates';

  @override
  String get addNewStory => 'إضافة قصة جديدة';

  @override
  String get updateYourProfile => 'تحديث ملفك الشخصي';

  @override
  String get configureYourAccountPrivacy => 'تكوين خصوصية حسابك';

  @override
  String get youInPublicSearch => 'أنت في البحث العام';

  @override
  String get yourProfileAppearsInPublicSearchAndAddingForGroups =>
      'يظهر ملفك الشخصي في البحث العام والإضافة للمجموعات';

  @override
  String get yourLastSeen => 'آخر ظهور لك';

  @override
  String get yourLastSeenInChats => 'آخر ظهور لك في الدردشات';

  @override
  String get startNewChatWithYou => 'بدء دردشة جديدة معك';

  @override
  String get yourStory => 'قصتك';

  @override
  String get forRequest => 'للطلب';

  @override
  String get public => 'عام';

  @override
  String get createYourStory => 'إنشاء قصتك';

  @override
  String get shareYourStatus => 'شارك حالتك';

  @override
  String get oneSeenMessage => 'رسالة واحدة تمت رؤيتها';

  @override
  String get messageHasBeenViewed => 'تمت مشاهدة الرسالة';

  @override
  String get clickToSee => 'انقر لرؤية';

  @override
  String get images => 'صور';

  @override
  String get switchAccount => 'Switch Account';

  @override
  String get addAccount => 'Add Account';

  @override
  String get manageYourAccounts => 'Manage your accounts';

  @override
  String get addAnotherAccount => 'Add another account';

  @override
  String get selectAccountToSwitchTo => 'Select account to switch to';

  @override
  String get errorLoadingAccounts => 'Error loading accounts';

  @override
  String get noAccountsFound => 'No accounts found';

  @override
  String get active => 'Active';

  @override
  String get removeAccount => 'Remove Account';

  @override
  String get areYouSureRemoveAccount =>
      'Are you sure you want to remove this account?';

  @override
  String get accountRemoved => 'Account removed';

  @override
  String get errorRemovingAccount => 'Error removing account';

  @override
  String switchedToAccount(Object name) {
    return 'Switched to $name';
  }

  @override
  String get errorSwitchingAccount => 'Error switching account';

  @override
  String get accountAddedSuccessfully => 'Account added successfully';

  @override
  String get addProfilePicture => 'Add Profile Picture';

  @override
  String get addProfilePictureSubtitle =>
      'Add a profile picture to help others recognize you';

  @override
  String get pleaseSelectProfilePicture => 'Please select a profile picture';

  @override
  String get uploading => 'Uploading...';

  @override
  String get continueText => 'Continue';

  @override
  String get profilePictureRequired =>
      'A profile picture is required to continue. Please select an image to upload.';
}
