{"@@locale": "en", "done": "Done", "loading": "Loading ...", "messageHasBeenDeleted": "Message has been deleted", "mute": "Mute", "cancel": "Cancel", "typing": "Typing...", "ok": "OK", "recording": "Recording...", "connecting": "Connecting...", "deleteYouCopy": "Delete your copy", "unMute": "Un mute", "delete": "Delete", "report": "Report", "leaveGroup": "Leave group", "areYouSureToPermitYourCopyThisActionCantUndo": "Are you sure to permit your copy? This action can't undo", "areYouSureToLeaveThisGroupThisActionCantUndo": "Are you sure to leave this group? This action can't undo", "leaveGroupAndDeleteYourMessageCopy": "Leave group and delete your message copy", "vMessageInfoTrans": "Message info", "updateTitleTo": "Update title to", "updateImage": "Update image", "joinedBy": "Joined by", "promotedToAdminBy": "Promoted to admin by", "dismissedToMemberBy": "Dismissed to member by", "leftTheGroup": "Left the group", "you": "You", "kickedBy": "Kicked by", "groupCreatedBy": "Group created by", "addedYouToNewBroadcast": "Added you to new broadcast", "download": "Download", "copy": "Copy", "info": "Info", "share": "Share", "forward": "Forward", "reply": "Reply", "reacted": "Reacted", "replied": "Replied", "deleteFromAll": "Delete from all", "deleteFromMe": "Delete from me", "downloading": "Downloading...", "fileHasBeenSavedTo": "File has been saved to", "online": "Online", "youDontHaveAccess": "You don't have access", "replyToYourSelf": "Reply to your self", "repliedToYourSelf": "Replied to your self", "audioCall": "Audio call", "ring": "Ring", "canceled": "Canceled", "timeout": "Timeout", "rejected": "Rejected", "finished": "Finished", "inCall": "In call", "sessionEnd": "Session end", "yesterday": "Yesterday", "today": "Today", "textFieldHint": "Type a message ...", "files": "Files", "location": "Location", "shareMediaAndLocation": "Share media and location", "thereIsVideoSizeBiggerThanAllowedSize": "There is video size bigger than allowed size", "thereIsFileHasSizeBiggerThanAllowedSize": "There is file has size bigger than allowed size", "makeCall": "Make call", "areYouWantToMakeVideoCall": "Are you want to make video call?", "areYouWantToMakeVoiceCall": "Are you want to make voice call?", "vMessagesInfoTrans": "Messages info", "star": "Star", "minutes": "Minutes", "sendMessage": "Send message", "deleteUser": "Delete user", "actions": "Actions", "youAreAboutToDeleteThisUserFromYourList": "You are about to delete this user from your list", "updateBroadcastTitle": "Update broadcast title", "usersAddedSuccessfully": "Users added successfully", "broadcastSettings": "Broadcast settings", "addParticipants": "Add Participants", "broadcastParticipants": "Broadcast Participants", "updateGroupDescription": "Update group description", "updateGroupTitle": "Update group title", "groupSettings": "Group settings", "description": "Description", "muteNotifications": "Mute notifications", "groupParticipants": "Group Participants", "blockUser": "Block user", "areYouSureToBlock": "Are you sure to block", "userPage": "User page", "starMessage": "Star message", "showMedia": "Show media", "reportUser": "Report user", "groupName": "group name", "changeSubject": "Change subject", "titleIsRequired": "Title is required", "createBroadcast": "Create Broadcast", "broadcastName": "Broadcast name", "createGroup": "Create Group", "forgetPassword": "Forget Password?", "globalSearch": "Global Search", "dismissesToMember": "Dismisses to member", "setToAdmin": "Set to admin", "kickMember": "Kick member", "youAreAboutToDismissesToMember": "You are about to dismisses to member", "youAreAboutToKick": "You are about to kick", "groupMembers": "Group Members", "tapForPhoto": "Tap for photo", "weHighRecommendToDownloadThisUpdate": "We high recommend to download this update", "newGroup": "New group", "newBroadcast": "New broadcast", "starredMessage": "Starred message", "settings": "Settings", "chats": "CHATS", "recentUpdates": "Recent updates", "startChat": "Start chat", "newUpdateIsAvailable": "New update is available", "emailNotValid": "Email not valid", "passwordMustHaveValue": "Password must have value", "error": "Error", "password": "Password", "login": "<PERSON><PERSON>", "needNewAccount": "Need new account?", "register": "Register", "nameMustHaveValue": "Name must have value", "passwordNotMatch": "Password not match", "name": "Name", "email": "Email", "confirmPassword": "Confirm password", "alreadyHaveAnAccount": "Already have an account?", "logOut": "Log out", "back": "Back", "sendCodeToMyEmail": "Send code to my email", "invalidLoginData": "Invalid login data", "userEmailNotFound": "User email not found", "yourAccountBlocked": "Your account has been baned", "yourAccountDeleted": "Your account has been deleted", "userAlreadyRegister": "User already register", "codeHasBeenExpired": "Code has been expired", "invalidCode": "Invalid code", "whileAuthCanFindYou": "While authentication cannot find you", "userRegisterStatusNotAcceptedYet": "User register status not accepted yet", "deviceHasBeenLogoutFromAllDevices": "Device has been logout from all devices", "userDeviceSessionEndDeviceDeleted": "User device session end device deleted", "noCodeHasBeenSendToYouToVerifyYourEmail": "No code has been send to you to verify your email", "roomAlreadyInCall": "Room already in call", "peerUserInCallNow": "User in call now", "callNotAllowed": "Call not allowed", "peerUserDeviceOffline": "Peer user device offline", "emailMustBeValid": "Email must be valid", "wait2MinutesToSendMail": "Wait 2 minutes to send mail", "codeMustEqualToSixNumbers": "Code must equal to six numbers", "newPasswordMustHaveValue": "New password must have value", "confirmPasswordMustHaveValue": "Confirm password must have value", "congregationsYourAccountHasBeenAccepted": "Congregations your account has been accepted", "yourAccountIsUnderReview": "Your account is under review", "waitingList": "Waiting List", "welcome": "Welcome", "retry": "Retry", "deleteMember": "Delete member", "profile": "Profile", "broadcastInfo": "Broadcast info", "updateTitle": "Update title", "members": "Members", "addMembers": "Add Members", "success": "Success", "media": "Media", "docs": "Docs", "links": "Links", "soon": "Soon", "unStar": "Un star", "updateGroupDescriptionWillUpdateAllGroupMembers": "Update group description will update all group members", "updateNickname": "Update nickname", "groupInfo": "Group info", "youNotParticipantInThisGroup": "You not participant in this group", "search": "Search", "mediaLinksAndDocs": "Media, Links, and Docs", "starredMessages": "Starred Messages", "nickname": "Nickname", "none": "None", "yes": "Yes", "no": "No", "exitGroup": "Exit Group", "clickToAddGroupDescription": "Click to add group description", "unBlockUser": "Un block user", "areYouSureToUnBlock": "Are you sure to un block", "contactInfo": "Contact info", "audio": "Audio", "video": "Video", "hiIamUse": "Hi iam using", "on": "On", "off": "Off", "unBlock": "Un Block", "block": "Block", "chooseAtLestOneMember": "Choose at lest one member", "close": "Close", "next": "Next", "appMembers": "App members", "create": "Create", "upgradeToAdmin": "Upgrade to admin", "update": "Update", "deleteChat": "Delete chat", "clearChat": "Clear chat", "showHistory": "Show history", "groupIcon": "Group icon", "tapToSelectAnIcon": "Tap to select an icon", "groupDescription": "Group description", "more": "More", "messageInfo": "Message info", "successfullyDownloadedIn": "Successfully downloaded in", "delivered": "Delivered", "read": "Read", "orLoginWith": "or login with", "resetPassword": "Reset password", "otpCode": "OTP Code", "newPassword": "New password", "areYouSure": "Are you sure?", "broadcastMembers": "Broadcast Members", "phone": "Phone", "users": "Users", "calls": "Calls", "yourAreAboutToLogoutFromThisAccount": "Your are about to logout from this account", "noUpdatesAvailableNow": "No updates available now", "dataPrivacy": "Data privacy", "allDataHasBeenBackupYouDontNeedToManageSaveTheDataByYourself": "All data has been backup you don\\'t want need to manage save the data by your self! if you logout and login again you will see all chats same for web version", "account": "Account", "linkedDevices": "Linked Devices", "storageAndData": "Storage and Data", "tellAFriend": "Tell a friend", "help": "Help", "blockedUsers": "Blocked users", "inAppAlerts": "In app alerts", "language": "Language", "adminNotification": "Admin notification", "checkForUpdates": "Check for updates", "linkByQrCode": "Link By Qr Code", "deviceStatus": "Device status", "desktopAndOtherDevices": "Desktop, and other devices", "linkADeviceSoon": "<PERSON> a Device (Soon)", "lastActiveFrom": "Last active from", "tapADeviceToEditOrLogOut": "Tap a device to edit or log out.", "contactUs": "Contact Us", "supportChatSoon": "Support chat (Soon)", "updateYourName": "Update your name", "updateYourBio": "Update your bio", "edit": "Edit", "about": "About", "oldPassword": "Old password", "deleteMyAccount": "Delete my account", "passwordHasBeenChanged": "Password has been changed", "logoutFromAllDevices": "Logout from all devices?", "updateYourPassword": "Update your password", "enterNameAndAddOptionalProfilePicture": "Enter your name and add an optional profile picture", "privacyPolicy": "Privacy policy", "chat": "Cha<PERSON>", "send": "Send", "reportHasBeenSubmitted": "Your report has been submitted", "offline": "Offline", "harassmentOrBullyingDescription": "Harassment or Bullying: This option allows users to report individuals who are targeting them or others with harassing messages, threats, or other forms of bullying.", "spamOrScamDescription": "Spam or Scam: This option would be for users to report accounts that are sending spam messages, unsolicited advertisements, or are attempting to scam others.", "areYouSureToReportUserToAdmin": "Are you sure to submit report about this user to the admin?", "groupWith": "Group with", "inappropriateContentDescription": "Inappropriate Content: Users can select this option to report any sexually explicit material, hate speech, or other content that violates community standards.", "otherCategoryDescription": "Other: This catch-all category can be used for violations that don't easily fit into the above categories. It might be helpful to include a text box for users to provide additional details.", "explainWhatHappens": "Explain here what happens", "loginAgain": "Login again!", "yourSessionIsEndedPleaseLoginAgain": "Your session is ended please login again!", "aboutToBlockUserWithConsequences": "You are about to block this user. You can't send him chats and can't add him to groups or broadcast!", "youAreAboutToDeleteYourAccountYourAccountWillNotAppearAgainInUsersList": "You are about to delete your account your account will not appears again in the users list", "admin": "Admin", "member": "Member", "creator": "Creator", "currentDevice": "Current device", "visits": "Visits", "chooseRoom": "Choose room", "deleteThisDeviceDesc": "Deleting this device means instantly logout this device", "youAreAboutToUpgradeToAdmin": "You are about to upgrade to admin", "microphonePermissionMustBeAccepted": "Microphone permission must be accepted", "microphoneAndCameraPermissionMustBeAccepted": "Microphone and camera permission must be accepted", "loginNowAllowedNowPleaseTryAgainLater": "<PERSON><PERSON> now allowed. Please try again later.", "dashboard": "Dashboard", "notification": "Notification", "total": "Total", "blocked": "Blocked", "deleted": "Deleted", "accepted": "Accepted", "notAccepted": "Not Accepted", "web": "Web", "android": "Android", "macOs": "macOS", "windows": "Windows", "other": "Other", "totalVisits": "Total Visits", "totalMessages": "Total Messages", "textMessages": "Text Messages", "imageMessages": "Image Messages", "videoMessages": "Video Messages", "voiceMessages": "Voice Messages", "fileMessages": "File Messages", "infoMessages": "Info Messages", "voiceCallMessages": "Voice Call Messages", "videoCallMessages": "Video Call Messages", "locationMessages": "Location Messages", "directChat": "Direct Chat", "group": "Group", "broadcast": "Broadcast", "messageCounter": "Message Counter", "roomCounter": "Room Counter", "countries": "Countries", "devices": "Devices", "notificationTitle": "Notification Title", "notificationDescription": "Notification Description", "notificationsPage": "Notifications Page", "updateFeedBackEmail": "Update Feedback <PERSON><PERSON>", "setMaxMessageForwardAndShare": "Set Max Message Forward and Share", "setNewPrivacyPolicyUrl": "Set New Privacy Policy URL", "forgetPasswordExpireTime": "Forget Password Expire Time", "callTimeoutInSeconds": "Call Timeout in Seconds", "setMaxGroupMembers": "Set Max Group Members", "setMaxBroadcastMembers": "Set Max Broadcast Members", "allowCalls": "Allow Calls", "ifThisOptionEnabledTheVideoAndVoiceCallWillBeAllowed": "If this option is enabled, the video and voice call will be allowed.", "allowAds": "Allow Ads", "allowMobileLogin": "Allow Mobile Login", "allowWebLogin": "Allow Web Login", "messages": "Messages", "appleStoreAppUrl": "Apple Store App URL", "googlePlayAppUrl": "Google Play App URL", "privacyUrl": "Privacy URL", "feedBackEmail": "Feed<PERSON>", "ifThisOptionDisabledTheSendChatFilesImageVideosAndLocationWillBeBlocked": "If this option is disabled, sending chat files, images, videos, and location will be blocked.", "allowSendMedia": "Allow Send Media", "ifThisOptionDisabledTheCreateChatBroadcastWillBeBlocked": "If this option is disabled, creating chat broadcast will be blocked.", "allowCreateBroadcast": "Allow Create Broadcast", "ifThisOptionDisabledTheCreateChatGroupsWillBeBlocked": "If this option is disabled, creating chat groups will be blocked.", "allowCreateGroups": "Allow Create Groups", "ifThisOptionDisabledTheDesktopLoginOrRegisterWindowsMacWillBeBlocked": "If this option is disabled, the desktop login or register (Windows, Mac) will be blocked.", "allowDesktopLogin": "Allow Desktop Login", "ifThisOptionDisabledTheWebLoginOrRegisterWillBeBlocked": "If this option is disabled, the web login or register will be blocked.", "ifThisOptionDisabledTheMobileLoginOrRegisterWillBeBlockedOnAndroidIosOnly": "If this option is disabled, the mobile login or register will be blocked on Android and iOS only.", "ifThisOptionEnabledTheGoogleAdsBannerWillAppearInChats": "If this option is enabled, the Google Ads banner will appear in chats.", "userProfile": "User Profile", "userInfo": "User Info", "fullName": "Full Name", "bio": "Bio", "noBio": "No Bio", "verifiedAt": "Verified At", "country": "Country", "registerStatus": "Register Status", "registerMethod": "Register Method", "banTo": "Ban <PERSON>", "deletedAt": "Deleted At", "createdAt": "Created At", "updatedAt": "Updated At", "reports": "Reports", "clickToSeeAllUserDevicesDetails": "Click to see all user devices details", "allDeletedMessages": "All Deleted Messages", "voiceCallMessage": "Voice Call Message", "totalRooms": "Total Rooms", "directRooms": "Direct Rooms", "userAction": "User Action", "status": "Status", "joinedAt": "Joined At", "saveLogin": "Save Login", "passwordIsRequired": "Password is required", "verified": "Verified", "pending": "Pending", "ios": "iOS", "descriptionIsRequired": "Description is required", "seconds": "Seconds", "clickToSeeAllUserInformations": "Click to see all user information", "clickToSeeAllUserCountries": "Click to see all user countries", "clickToSeeAllUserMessagesDetails": "Click to see all user messages details", "clickToSeeAllUserRoomsDetails": "Click to see all user rooms details", "clickToSeeAllUserReports": "Click to see all user reports", "banAt": "Ban at", "nowYouLoginAsReadOnlyAdminAllEditYouDoneWillNotAppliedDueToThisIsTestVersion": "Now you login as read-only admin. All edits you make will not be applied due to this being a test version.", "createStory": "Create Story", "writeACaption": "Write a caption...", "storyCreatedSuccessfully": "Story Created Successfully", "stories": "Stories", "clear": "Clear", "clearCallsConfirm": "Clear calls confirm", "chooseHowAutomaticDownloadWorks": "Choose how automatic download works", "whenUsingMobileData": "When using mobile data", "whenUsingWifi": "When using Wi-Fi", "image": "Image", "myPrivacy": "My Privacy", "createTextStory": "Create Text Story", "createMediaStory": "Create Media Story", "camera": "Camera", "gallery": "Gallery", "recentUpdate": "Recent update", "viewedUpdates": "Viewed updates", "addNewStory": "Add new story", "updateYourProfile": "Update your profile", "configureYourAccountPrivacy": "Configure your account privacy", "youInPublicSearch": "You in public search", "yourProfileAppearsInPublicSearchAndAddingForGroups": "Your profile appears in public search and adding for groups", "yourLastSeen": "Your last seen", "yourLastSeenInChats": "Your last seen in chats", "startNewChatWithYou": "Start new chat with you", "yourStory": "Your story", "forRequest": "For request", "public": "Public", "createYourStory": "Create your story", "shareYourStatus": "Share your status", "oneSeenMessage": "One seen message", "messageHasBeenViewed": "Message has been viewed", "clickToSee": "Click to see", "images": "Images", "switchAccount": "Switch Account", "addAccount": "Add Account", "manageYourAccounts": "Manage your accounts", "addAnotherAccount": "Add another account", "selectAccountToSwitchTo": "Select account to switch to", "errorLoadingAccounts": "Error loading accounts", "noAccountsFound": "No accounts found", "active": "Active", "removeAccount": "Remove Account", "areYouSureRemoveAccount": "Are you sure you want to remove this account?", "accountRemoved": "Account removed", "errorRemovingAccount": "Error removing account", "switchedToAccount": "Switched to {name}", "errorSwitchingAccount": "Error switching account", "accountAddedSuccessfully": "Account added successfully", "addProfilePicture": "Add Profile Picture", "addProfilePictureSubtitle": "Add a profile picture to help others recognize you", "pleaseSelectProfilePicture": "Please select a profile picture", "uploading": "Uploading...", "continueText": "Continue", "profilePictureRequired": "A profile picture is required to continue. Please select an image to upload."}