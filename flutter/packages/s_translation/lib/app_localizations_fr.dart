// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for French (`fr`).
class AppLocalizationsFr extends AppLocalizations {
  AppLocalizationsFr([String locale = 'fr']) : super(locale);

  @override
  String get done => 'Fait';

  @override
  String get loading => 'Chargement ...';

  @override
  String get messageHasBeenDeleted => 'Le message a été supprimé';

  @override
  String get mute => 'Mute';

  @override
  String get cancel => 'Annuler';

  @override
  String get typing => 'Dactylographie...';

  @override
  String get ok => 'D\'ACCORD';

  @override
  String get recording => 'Enregistrement...';

  @override
  String get connecting => 'De liaison...';

  @override
  String get deleteYouCopy => 'Supprimer votre copie';

  @override
  String get unMute => 'Réactiver à être confronté à';

  @override
  String get delete => 'Supprimer';

  @override
  String get report => 'Rapport';

  @override
  String get leaveGroup => 'Quitter le groupe';

  @override
  String get areYouSureToPermitYourCopyThisActionCantUndo =>
      'Êtes-vous sûr d\'autoriser votre copie?Cette action ne peut pas défaire';

  @override
  String get areYouSureToLeaveThisGroupThisActionCantUndo =>
      'Êtes-vous sûr de quitter ce groupe?Cette action ne peut pas défaire';

  @override
  String get leaveGroupAndDeleteYourMessageCopy =>
      'Laissez le groupe et supprimez votre copie de message';

  @override
  String get vMessageInfoTrans => 'Informations sur le message';

  @override
  String get updateTitleTo => 'Mettre à jour le titre de';

  @override
  String get updateImage => 'Mettre à jour l\'image';

  @override
  String get joinedBy => 'Rejoint par';

  @override
  String get promotedToAdminBy => 'Promu administrer par';

  @override
  String get dismissedToMemberBy => 'Licencié au membre par';

  @override
  String get leftTheGroup => 'Quitté le groupe';

  @override
  String get you => 'Toi';

  @override
  String get kickedBy => 'Relâché par';

  @override
  String get groupCreatedBy => 'Groupe créé par';

  @override
  String get addedYouToNewBroadcast => 'Vous a ajouté à une nouvelle diffusion';

  @override
  String get download => 'Télécharger';

  @override
  String get copy => 'Copie';

  @override
  String get info => 'Informations';

  @override
  String get share => 'Partager';

  @override
  String get forward => 'Avant';

  @override
  String get reply => 'Répondre';

  @override
  String get reacted => 'Reacted';

  @override
  String get replied => 'Replied';

  @override
  String get deleteFromAll => 'Supprimer de tous';

  @override
  String get deleteFromMe => 'Me supprimer';

  @override
  String get downloading => 'Téléchargement...';

  @override
  String get fileHasBeenSavedTo => 'Le fichier a été enregistré pour';

  @override
  String get online => 'En ligne';

  @override
  String get youDontHaveAccess => 'Vous n\'avez pas accès';

  @override
  String get replyToYourSelf => 'Répondre à vous-même';

  @override
  String get repliedToYourSelf => 'Répondu à toi-même';

  @override
  String get audioCall => 'Appel audio';

  @override
  String get ring => 'Anneau';

  @override
  String get canceled => 'Annulé';

  @override
  String get timeout => 'Temps mort';

  @override
  String get rejected => 'Rejeté';

  @override
  String get finished => 'Fini';

  @override
  String get inCall => 'En appel';

  @override
  String get sessionEnd => 'Fin de session';

  @override
  String get yesterday => 'Hier';

  @override
  String get today => 'Aujourd\'hui';

  @override
  String get textFieldHint => 'Tapez un message ...';

  @override
  String get files => 'Fichiers';

  @override
  String get location => 'Emplacement';

  @override
  String get shareMediaAndLocation => 'Partager les médias et l\'emplacement';

  @override
  String get thereIsVideoSizeBiggerThanAllowedSize =>
      'Il y a une taille de vidéo plus grande que la taille autorisée';

  @override
  String get thereIsFileHasSizeBiggerThanAllowedSize =>
      'Il y a un fichier plus grand que la taille autorisée';

  @override
  String get makeCall => 'Faire appel';

  @override
  String get areYouWantToMakeVideoCall =>
      'Voulez-vous passer des appels vidéo?';

  @override
  String get areYouWantToMakeVoiceCall => 'Voulez-vous passer la voix?';

  @override
  String get vMessagesInfoTrans => 'Informations sur les messages';

  @override
  String get star => 'Étoile';

  @override
  String get minutes => 'Minutes';

  @override
  String get sendMessage => 'Envoyer un message';

  @override
  String get deleteUser => 'Supprimer l\'utilisateur';

  @override
  String get actions => 'Actes';

  @override
  String get youAreAboutToDeleteThisUserFromYourList =>
      'Vous êtes sur le point de supprimer cet utilisateur de votre liste';

  @override
  String get updateBroadcastTitle => 'Mettre à jour le titre de diffusion';

  @override
  String get usersAddedSuccessfully =>
      'Les utilisateurs ont ajouté avec succès';

  @override
  String get broadcastSettings => 'Paramètres de diffusion';

  @override
  String get addParticipants => 'Ajouter les participants';

  @override
  String get broadcastParticipants => 'Diffusion des participants';

  @override
  String get updateGroupDescription => 'Mettre à jour la description du groupe';

  @override
  String get updateGroupTitle => 'Mettre à jour le titre du groupe';

  @override
  String get groupSettings => 'Paramètres de groupe';

  @override
  String get description => 'Description';

  @override
  String get muteNotifications => 'Notifications muettes';

  @override
  String get groupParticipants => 'Participants au groupe';

  @override
  String get blockUser => 'Bloquer l\'utilisateur';

  @override
  String get areYouSureToBlock => 'Êtes-vous sûr de bloquer';

  @override
  String get userPage => 'Page utilisateur';

  @override
  String get starMessage => 'Message de star';

  @override
  String get showMedia => 'Montrer les médias';

  @override
  String get reportUser => 'Signaler l\'utilisateur';

  @override
  String get groupName => 'nom de groupe';

  @override
  String get changeSubject => 'Changer de sujet';

  @override
  String get titleIsRequired => 'Le titre est requis';

  @override
  String get createBroadcast => 'Créer une diffusion';

  @override
  String get broadcastName => 'Nom de diffusion';

  @override
  String get createGroup => 'Créer un groupe';

  @override
  String get forgetPassword => 'Oublier le mot de passe?';

  @override
  String get globalSearch => 'Recherche globale';

  @override
  String get dismissesToMember => 'Rejette au membre';

  @override
  String get setToAdmin => 'Réglé sur l\'administration';

  @override
  String get kickMember => 'Membre du coup de pied';

  @override
  String get youAreAboutToDismissesToMember =>
      'Vous êtes sur le point de rejeter au membre';

  @override
  String get youAreAboutToKick =>
      'Tu es sur le point de donner des coups de pied';

  @override
  String get groupMembers => 'Membres du groupe';

  @override
  String get tapForPhoto => 'Taper pour la photo';

  @override
  String get weHighRecommendToDownloadThisUpdate =>
      'Nous recommandons haut de télécharger cette mise à jour';

  @override
  String get newGroup => 'Nouveau groupe';

  @override
  String get newBroadcast => 'Nouvelle diffusion';

  @override
  String get starredMessage => 'Message étoilé';

  @override
  String get settings => 'Paramètres';

  @override
  String get chats => 'CHATS';

  @override
  String get recentUpdates => 'Mises à jour récentes';

  @override
  String get startChat => 'Commencer à discuter';

  @override
  String get newUpdateIsAvailable => 'Une nouvelle mise à jour est disponible';

  @override
  String get emailNotValid => 'E-mail non valide';

  @override
  String get passwordMustHaveValue => 'Le mot de passe doit avoir une valeur';

  @override
  String get error => 'Erreur';

  @override
  String get password => 'Mot de passe';

  @override
  String get login => 'Se connecter';

  @override
  String get needNewAccount => 'Besoin d\'un nouveau compte?';

  @override
  String get register => 'Registre';

  @override
  String get nameMustHaveValue => 'Le nom doit avoir une valeur';

  @override
  String get passwordNotMatch => 'Le mot de passe ne correspond pas';

  @override
  String get name => 'Nom';

  @override
  String get email => 'E-mail';

  @override
  String get confirmPassword => 'Confirmez le mot de passe';

  @override
  String get alreadyHaveAnAccount => 'Vous avez déjà un compte?';

  @override
  String get logOut => 'Déconnecter';

  @override
  String get back => 'Dos';

  @override
  String get sendCodeToMyEmail => 'Envoyer du code à mon e-mail';

  @override
  String get invalidLoginData => 'Données de connexion non valides';

  @override
  String get userEmailNotFound => 'E-mail utilisateur introuvable';

  @override
  String get yourAccountBlocked => 'Votre compte a été banni';

  @override
  String get yourAccountDeleted => 'Votre compte a été supprimé';

  @override
  String get userAlreadyRegister => 'L\'utilisateur s\'inscrit déjà';

  @override
  String get codeHasBeenExpired => 'Le code a été expiré';

  @override
  String get invalidCode => 'Code non valide';

  @override
  String get whileAuthCanFindYou =>
      'Bien que l\'authentification ne puisse pas vous trouver';

  @override
  String get userRegisterStatusNotAcceptedYet =>
      'État du registre des utilisateurs non accepté';

  @override
  String get deviceHasBeenLogoutFromAllDevices =>
      'Le périphérique a été déconnecté de tous les appareils';

  @override
  String get userDeviceSessionEndDeviceDeleted =>
      'Appareil de la session du périphérique de session de la session supprimée';

  @override
  String get noCodeHasBeenSendToYouToVerifyYourEmail =>
      'Aucun code ne vous a été envoyé pour vérifier votre e-mail';

  @override
  String get roomAlreadyInCall => 'Chambre déjà en appel';

  @override
  String get peerUserInCallNow => 'Utilisateur en appel maintenant';

  @override
  String get callNotAllowed => 'Appeler non autorisé';

  @override
  String get peerUserDeviceOffline =>
      'Appareil utilisateur de pairs hors ligne';

  @override
  String get emailMustBeValid => 'Le courrier électronique doit être valide';

  @override
  String get wait2MinutesToSendMail =>
      'Attendez 2 minutes pour envoyer le courrier';

  @override
  String get codeMustEqualToSixNumbers =>
      'Le code doit être égal à six nombres';

  @override
  String get newPasswordMustHaveValue =>
      'Le nouveau mot de passe doit avoir de la valeur';

  @override
  String get confirmPasswordMustHaveValue =>
      'Confirmer le mot de passe doit avoir une valeur';

  @override
  String get congregationsYourAccountHasBeenAccepted =>
      'Congrégations Votre compte a été accepté';

  @override
  String get yourAccountIsUnderReview => 'Votre compte est en cours d\'examen';

  @override
  String get waitingList => 'Liste d\'attente';

  @override
  String get welcome => 'Welcome';

  @override
  String get retry => 'Réessayer';

  @override
  String get deleteMember => 'Supprimer';

  @override
  String get profile => 'Profil';

  @override
  String get broadcastInfo => 'Informations sur la diffusion';

  @override
  String get updateTitle => 'Mettre à jour le titre';

  @override
  String get members => 'Membres';

  @override
  String get addMembers => 'Ajouter des membres';

  @override
  String get success => 'Succès';

  @override
  String get media => 'Médias';

  @override
  String get docs => 'Docs';

  @override
  String get links => 'Links';

  @override
  String get soon => 'Bientôt';

  @override
  String get unStar => 'Star de l\'ONU';

  @override
  String get updateGroupDescriptionWillUpdateAllGroupMembers =>
      'Mettre à jour la description du groupe mettra à jour tous les membres du groupe';

  @override
  String get updateNickname => 'Mettre à jour le surnom';

  @override
  String get groupInfo => 'Informations de groupe';

  @override
  String get youNotParticipantInThisGroup =>
      'Vous ne participiez pas à ce groupe';

  @override
  String get search => 'Recherche';

  @override
  String get mediaLinksAndDocs => 'Médias, liens et documents';

  @override
  String get starredMessages => 'Messages joués';

  @override
  String get nickname => 'Surnom';

  @override
  String get none => 'Aucun';

  @override
  String get yes => 'Oui';

  @override
  String get no => 'Non';

  @override
  String get exitGroup => 'Groupe de sortie';

  @override
  String get clickToAddGroupDescription =>
      'Cliquez pour ajouter la description du groupe';

  @override
  String get unBlockUser => 'Débloquer';

  @override
  String get areYouSureToUnBlock => 'Êtes-vous sûr de débloquer';

  @override
  String get contactInfo => 'Coordonnées';

  @override
  String get audio => 'Audio';

  @override
  String get video => 'Vidéo';

  @override
  String get hiIamUse => 'Salut iam en utilisant';

  @override
  String get on => 'Sur';

  @override
  String get off => 'Désactivé';

  @override
  String get unBlock => 'Bloc de l\'ONU';

  @override
  String get block => 'Bloc';

  @override
  String get chooseAtLestOneMember => 'Choisissez au moins un membre';

  @override
  String get close => 'Fermer';

  @override
  String get next => 'Suivant';

  @override
  String get appMembers => 'Membres de l\'application';

  @override
  String get create => 'Créer';

  @override
  String get upgradeToAdmin => 'Passer à l\'administrateur';

  @override
  String get update => 'Mise à jour';

  @override
  String get deleteChat => 'Supprimer le chat';

  @override
  String get clearChat => 'Chat claire';

  @override
  String get showHistory => 'Montrer l\'histoire';

  @override
  String get groupIcon => 'Icône de groupe';

  @override
  String get tapToSelectAnIcon => 'Appuyez pour sélectionner une icône';

  @override
  String get groupDescription => 'Description du groupe';

  @override
  String get more => 'Plus';

  @override
  String get messageInfo => 'Informations sur le message';

  @override
  String get successfullyDownloadedIn => 'Téléchargé avec succès dans';

  @override
  String get delivered => 'Livré';

  @override
  String get read => 'Lire';

  @override
  String get orLoginWith => 'ou connecter avec';

  @override
  String get resetPassword => 'Réinitialiser le mot de passe';

  @override
  String get otpCode => 'Code OTP';

  @override
  String get newPassword => 'Nouveau mot de passe';

  @override
  String get areYouSure => 'Es-tu sûr?';

  @override
  String get broadcastMembers => 'Membres de la diffusion';

  @override
  String get phone => 'Téléphone';

  @override
  String get users => 'Utilisateurs';

  @override
  String get calls => 'Appels';

  @override
  String get yourAreAboutToLogoutFromThisAccount =>
      'Vous êtes sur le point de vous connecter à partir de ce compte';

  @override
  String get noUpdatesAvailableNow =>
      'Aucune mise à jour disponible maintenant';

  @override
  String get dataPrivacy => 'Confidentialité des données';

  @override
  String get allDataHasBeenBackupYouDontNeedToManageSaveTheDataByYourself =>
      'Toutes les données ont été une sauvegarde, vous ne voulez pas avoir besoin de gérer les données de vous-même!Si vous vous déconnectez et vous vous connectez à nouveau, vous verrez tous les chats identiques pour la version Web';

  @override
  String get account => 'Compte';

  @override
  String get linkedDevices => 'Dispositifs liés';

  @override
  String get storageAndData => 'Stockage et données';

  @override
  String get tellAFriend => 'Dire à un ami';

  @override
  String get help => 'Aide';

  @override
  String get blockedUsers => 'Utilisateurs bloqués';

  @override
  String get inAppAlerts => 'Dans les alertes d\'applications';

  @override
  String get language => 'Langue';

  @override
  String get adminNotification => 'Notification administrative';

  @override
  String get checkForUpdates => 'Vérifier les mises à jour';

  @override
  String get linkByQrCode => 'Lien par code QR';

  @override
  String get deviceStatus => 'État de l\'appareil';

  @override
  String get desktopAndOtherDevices => 'Bureau et autres appareils';

  @override
  String get linkADeviceSoon => 'Lier un appareil (bientôt)';

  @override
  String get lastActiveFrom => 'Dernier actif de';

  @override
  String get tapADeviceToEditOrLogOut =>
      'Appuyez sur un appareil pour modifier ou déconnectez-vous.';

  @override
  String get contactUs => 'Contactez-nous';

  @override
  String get supportChatSoon => 'Soutenir le chat (bientôt)';

  @override
  String get updateYourName => 'Mettez à jour votre nom';

  @override
  String get updateYourBio => 'Mettez à jour votre bio';

  @override
  String get edit => 'Modifier';

  @override
  String get about => 'À propos';

  @override
  String get oldPassword => 'Ancien mot de passe';

  @override
  String get deleteMyAccount => 'Supprimer mon compte';

  @override
  String get passwordHasBeenChanged => 'Le mot de passe a été modifié';

  @override
  String get logoutFromAllDevices => 'Vous connecte à tous les appareils?';

  @override
  String get updateYourPassword => 'Mettez à jour votre mot de passe';

  @override
  String get enterNameAndAddOptionalProfilePicture =>
      'Entrez votre nom et ajoutez une photo de profil en option';

  @override
  String get privacyPolicy => 'Politique de confidentialité';

  @override
  String get chat => 'Chat';

  @override
  String get send => 'Envoyer';

  @override
  String get reportHasBeenSubmitted => 'Votre rapport a été soumis';

  @override
  String get offline => 'Hors ligne';

  @override
  String get harassmentOrBullyingDescription =>
      'CHARMEMENT OU L\'INTÉMINATION: Cette option permet aux utilisateurs de signaler que les personnes qui les ciblent ou d\'autres avec des messages harcelants, des menaces ou d\'autres formes d\'intimidation.';

  @override
  String get spamOrScamDescription =>
      'Spam ou arnaque: cette option serait que les utilisateurs signalent des comptes qui envoient des messages de spam, des publicités non sollicitées ou tentent d\'arracher d\'autres.';

  @override
  String get areYouSureToReportUserToAdmin =>
      'Êtes-vous sûr de soumettre un rapport sur cet utilisateur à l\'administrateur?';

  @override
  String get groupWith => 'Se regrouper avec';

  @override
  String get inappropriateContentDescription =>
      'Contenu inapproprié: les utilisateurs peuvent sélectionner cette option pour signaler tout matériel sexuellement explicite, discours de haine ou autre contenu qui viole les normes communautaires.';

  @override
  String get otherCategoryDescription =>
      'Autre: cette catégorie fourre-tout peut être utilisée pour des violations qui ne s\'intègrent pas facilement dans les catégories ci-dessus.Il peut être utile d\'inclure une zone de texte pour que les utilisateurs fournissent des détails supplémentaires.';

  @override
  String get explainWhatHappens => 'Expliquez ici ce qui se passe';

  @override
  String get loginAgain => 'Connectez-vous à nouveau!';

  @override
  String get yourSessionIsEndedPleaseLoginAgain =>
      'Votre session est terminée, veuillez vous connecter à nouveau!';

  @override
  String get aboutToBlockUserWithConsequences =>
      'Vous êtes sur le point de bloquer cet utilisateur.Vous ne pouvez pas lui envoyer de discussions et ne pouvez pas l\'ajouter à des groupes ou diffuser!';

  @override
  String get youAreAboutToDeleteYourAccountYourAccountWillNotAppearAgainInUsersList =>
      'Vous êtes sur le point de supprimer votre compte, votre compte n\'apparaîtra plus dans la liste des utilisateurs';

  @override
  String get admin => 'Administrer';

  @override
  String get member => 'Membre';

  @override
  String get creator => 'Créateur';

  @override
  String get currentDevice => 'Dispositif actuel';

  @override
  String get visits => 'Visites';

  @override
  String get chooseRoom => 'Choisir la chambre';

  @override
  String get deleteThisDeviceDesc =>
      'La suppression de cet appareil signifie déconnecter instantanément cet appareil';

  @override
  String get youAreAboutToUpgradeToAdmin =>
      'Vous êtes sur le point de passer à l\'administrateur';

  @override
  String get microphonePermissionMustBeAccepted =>
      'La permission du microphone doit être acceptée';

  @override
  String get microphoneAndCameraPermissionMustBeAccepted =>
      'L\'autorisation du microphone et de la caméra doit être acceptée';

  @override
  String get loginNowAllowedNowPleaseTryAgainLater =>
      'Connexion maintenant autorisée.Veuillez réessayer plus tard.';

  @override
  String get dashboard => 'Tableau de bord';

  @override
  String get notification => 'Notification';

  @override
  String get total => 'Total';

  @override
  String get blocked => 'Bloquée';

  @override
  String get deleted => 'Supprimé';

  @override
  String get accepted => 'Accepté';

  @override
  String get notAccepted => 'Non accepté';

  @override
  String get web => 'Web';

  @override
  String get android => 'Androïde';

  @override
  String get macOs => 'macos';

  @override
  String get windows => 'Fenêtre';

  @override
  String get other => 'Autre';

  @override
  String get totalVisits => 'Visites totales';

  @override
  String get totalMessages => 'Messages totaux';

  @override
  String get textMessages => 'SMS';

  @override
  String get imageMessages => 'Messages d\'image';

  @override
  String get videoMessages => 'Messages vidéo';

  @override
  String get voiceMessages => 'Messages vocaux';

  @override
  String get fileMessages => 'Messages de dossier';

  @override
  String get infoMessages => 'Messages d\'informations';

  @override
  String get voiceCallMessages => 'Messages d\'appel vocal';

  @override
  String get videoCallMessages => 'Messages d\'appel vidéo';

  @override
  String get locationMessages => 'Messages de localisation';

  @override
  String get directChat => 'Chat direct';

  @override
  String get group => 'Groupe';

  @override
  String get broadcast => 'Diffuser';

  @override
  String get messageCounter => 'Compteur de messages';

  @override
  String get roomCounter => 'Comptoir';

  @override
  String get countries => 'Pays';

  @override
  String get devices => 'Dispositifs';

  @override
  String get notificationTitle => 'Titre de notification';

  @override
  String get notificationDescription => 'Description de la notification';

  @override
  String get notificationsPage => 'Page de notifications';

  @override
  String get updateFeedBackEmail =>
      'Mettre à jour le courrier électronique des commentaires';

  @override
  String get setMaxMessageForwardAndShare =>
      'Set Max Message Forward and Share';

  @override
  String get setNewPrivacyPolicyUrl =>
      'Définir une nouvelle URL de politique de confidentialité';

  @override
  String get forgetPasswordExpireTime =>
      'Oubliez le mot de passe expirer l\'heure';

  @override
  String get callTimeoutInSeconds =>
      'Appelez le délai d\'attente en quelques secondes';

  @override
  String get setMaxGroupMembers => 'Définir les membres du groupe Max';

  @override
  String get setMaxBroadcastMembers =>
      'Définir les membres de la diffusion Max';

  @override
  String get allowCalls => 'Autoriser les appels';

  @override
  String get ifThisOptionEnabledTheVideoAndVoiceCallWillBeAllowed =>
      'Si cette option est activée, l\'appel vidéo et vocal sera autorisé.';

  @override
  String get allowAds => 'Autoriser les annonces';

  @override
  String get allowMobileLogin => 'Autoriser la connexion mobile';

  @override
  String get allowWebLogin => 'Autoriser la connexion Web';

  @override
  String get messages => 'Messages';

  @override
  String get appleStoreAppUrl => 'URL de l\'application Apple Store';

  @override
  String get googlePlayAppUrl => 'URL de l\'application Google Play';

  @override
  String get privacyUrl => 'URL de confidentialité';

  @override
  String get feedBackEmail => 'E-mail de commentaires';

  @override
  String get ifThisOptionDisabledTheSendChatFilesImageVideosAndLocationWillBeBlocked =>
      'Si cette option est désactivée, l\'envoi de fichiers de chat, d\'images, de vidéos et de localisation sera bloqué.';

  @override
  String get allowSendMedia => 'Autoriser l\'envoi de médias';

  @override
  String get ifThisOptionDisabledTheCreateChatBroadcastWillBeBlocked =>
      'Si cette option est désactivée, la création de diffusion de chat sera bloquée.';

  @override
  String get allowCreateBroadcast => 'Autoriser la création de diffusion';

  @override
  String get ifThisOptionDisabledTheCreateChatGroupsWillBeBlocked =>
      'Si cette option est désactivée, la création de groupes de chat sera bloquée.';

  @override
  String get allowCreateGroups => 'Autoriser la création de groupes';

  @override
  String get ifThisOptionDisabledTheDesktopLoginOrRegisterWindowsMacWillBeBlocked =>
      'Si cette option est désactivée, la connexion de bureau ou l\'enregistrement (Windows, Mac) sera bloqué.';

  @override
  String get allowDesktopLogin => 'Autoriser la connexion de bureau';

  @override
  String get ifThisOptionDisabledTheWebLoginOrRegisterWillBeBlocked =>
      'Si cette option est désactivée, la connexion Web ou l\'enregistrement sera bloqué.';

  @override
  String get ifThisOptionDisabledTheMobileLoginOrRegisterWillBeBlockedOnAndroidIosOnly =>
      'Si cette option est désactivée, la connexion ou l\'enregistrement mobile sera bloqué uniquement sur Android et iOS.';

  @override
  String get ifThisOptionEnabledTheGoogleAdsBannerWillAppearInChats =>
      'Si cette option est activée, la bannière Google Ads apparaîtra dans les chats.';

  @override
  String get userProfile => 'Profil de l\'utilisateur';

  @override
  String get userInfo => 'Informations sur l\'utilisateur';

  @override
  String get fullName => 'Nom et prénom';

  @override
  String get bio => 'Bio';

  @override
  String get noBio => 'Pas de bio';

  @override
  String get verifiedAt => 'Vérifié à';

  @override
  String get country => 'Pays';

  @override
  String get registerStatus => 'État de l\'enregistrement';

  @override
  String get registerMethod => 'Méthode d\'enregistrement';

  @override
  String get banTo => 'Banni jusqu\'à';

  @override
  String get deletedAt => 'Supprimé à';

  @override
  String get createdAt => 'Créé à';

  @override
  String get updatedAt => 'Mis à jour à';

  @override
  String get reports => 'Rapports';

  @override
  String get clickToSeeAllUserDevicesDetails =>
      'Cliquez pour voir tous les détails des appareils de l\'utilisateur';

  @override
  String get allDeletedMessages => 'Tous les messages supprimés';

  @override
  String get voiceCallMessage => 'Message d\'appel vocal';

  @override
  String get totalRooms => 'Nombre total de chambres';

  @override
  String get directRooms => 'Chambres directes';

  @override
  String get userAction => 'Action de l\'utilisateur';

  @override
  String get status => 'Statut';

  @override
  String get joinedAt => 'Rejoint le';

  @override
  String get saveLogin => 'Enregistrer la connexion';

  @override
  String get passwordIsRequired => 'Le mot de passe est requis';

  @override
  String get verified => 'Vérifié';

  @override
  String get pending => 'En attente';

  @override
  String get ios => 'iOS';

  @override
  String get descriptionIsRequired => 'Une description est requise';

  @override
  String get seconds => 'Secondes';

  @override
  String get clickToSeeAllUserInformations =>
      'Click to see all user information';

  @override
  String get clickToSeeAllUserCountries =>
      'Cliquez pour voir tous les pays des utilisateurs';

  @override
  String get clickToSeeAllUserMessagesDetails =>
      'Cliquez pour voir tous les détails des messages utilisateur';

  @override
  String get clickToSeeAllUserRoomsDetails =>
      'Click to see all user rooms details';

  @override
  String get clickToSeeAllUserReports => 'Click to see all user reports';

  @override
  String get banAt => 'Interdire';

  @override
  String get nowYouLoginAsReadOnlyAdminAllEditYouDoneWillNotAppliedDueToThisIsTestVersion =>
      'Maintenant, vous vous connectez en tant qu\'administrateur en lecture seule. Toutes les modifications que vous apportez ne seront pas appliquées car il s\'agit d\'une version de test.';

  @override
  String get createStory => 'Créer une histoire';

  @override
  String get writeACaption => 'Écrivez une légende...';

  @override
  String get storyCreatedSuccessfully => 'Histoire créée avec succès';

  @override
  String get stories => 'Histoires';

  @override
  String get clear => 'effacée';

  @override
  String get clearCallsConfirm => 'Effacer les appels confirmer';

  @override
  String get chooseHowAutomaticDownloadWorks =>
      'Choisissez le fonctionnement du téléchargement automatique';

  @override
  String get whenUsingMobileData => 'Lorsque vous utilisez des données mobiles';

  @override
  String get whenUsingWifi => 'Lorsque vous utilisez le Wi-Fi';

  @override
  String get image => 'Image';

  @override
  String get myPrivacy => 'Ma vie privée';

  @override
  String get createTextStory => 'Créer une histoire textuelle';

  @override
  String get createMediaStory => 'Créer une histoire médiatique';

  @override
  String get camera => 'Camera';

  @override
  String get gallery => 'Galerie';

  @override
  String get recentUpdate => 'Mise à jour récente';

  @override
  String get viewedUpdates => 'Viewed updates';

  @override
  String get addNewStory => 'Ajouter une nouvelle histoire';

  @override
  String get updateYourProfile => 'Mettez à jour votre profil';

  @override
  String get configureYourAccountPrivacy =>
      'Configurez la confidentialité de votre compte';

  @override
  String get youInPublicSearch => 'Vous dans la recherche publique';

  @override
  String get yourProfileAppearsInPublicSearchAndAddingForGroups =>
      'Votre profil apparaît dans la recherche publique et l\'ajout de groupes';

  @override
  String get yourLastSeen => 'Votre dernière vue';

  @override
  String get yourLastSeenInChats => 'Votre dernière vue dans les chats';

  @override
  String get startNewChatWithYou =>
      'Démarrez une nouvelle conversation avec vous';

  @override
  String get yourStory => 'Votre histoire';

  @override
  String get forRequest => 'Pour demande';

  @override
  String get public => 'Publique';

  @override
  String get createYourStory => 'Créez votre histoire';

  @override
  String get shareYourStatus => 'Partagez votre statut';

  @override
  String get oneSeenMessage => 'Un message vu';

  @override
  String get messageHasBeenViewed => 'Le message a été consulté';

  @override
  String get clickToSee => 'Cliquez pour voir';

  @override
  String get images => 'Images';

  @override
  String get switchAccount => 'Switch Account';

  @override
  String get addAccount => 'Add Account';

  @override
  String get manageYourAccounts => 'Manage your accounts';

  @override
  String get addAnotherAccount => 'Add another account';

  @override
  String get selectAccountToSwitchTo => 'Select account to switch to';

  @override
  String get errorLoadingAccounts => 'Error loading accounts';

  @override
  String get noAccountsFound => 'No accounts found';

  @override
  String get active => 'Active';

  @override
  String get removeAccount => 'Remove Account';

  @override
  String get areYouSureRemoveAccount =>
      'Are you sure you want to remove this account?';

  @override
  String get accountRemoved => 'Account removed';

  @override
  String get errorRemovingAccount => 'Error removing account';

  @override
  String switchedToAccount(Object name) {
    return 'Switched to $name';
  }

  @override
  String get errorSwitchingAccount => 'Error switching account';

  @override
  String get accountAddedSuccessfully => 'Account added successfully';

  @override
  String get addProfilePicture => 'Add Profile Picture';

  @override
  String get addProfilePictureSubtitle =>
      'Add a profile picture to help others recognize you';

  @override
  String get pleaseSelectProfilePicture => 'Please select a profile picture';

  @override
  String get uploading => 'Uploading...';

  @override
  String get continueText => 'Continue';

  @override
  String get profilePictureRequired =>
      'A profile picture is required to continue. Please select an image to upload.';
}
