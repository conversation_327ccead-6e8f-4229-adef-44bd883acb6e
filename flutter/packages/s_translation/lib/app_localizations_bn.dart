// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Bengali Bangla (`bn`).
class AppLocalizationsBn extends AppLocalizations {
  AppLocalizationsBn([String locale = 'bn']) : super(locale);

  @override
  String get done => 'সম্পন্ন';

  @override
  String get loading => 'লোড হচ্ছে ...';

  @override
  String get messageHasBeenDeleted => 'বার্তা মুছে ফেলা হয়েছে';

  @override
  String get mute => 'মিউট';

  @override
  String get cancel => 'বাতিল';

  @override
  String get typing => 'লেখা হচ্ছে...';

  @override
  String get ok => 'ঠিক আছে';

  @override
  String get recording => 'রেকর্ড হচ্ছে...';

  @override
  String get connecting => 'সংযোগ হচ্ছে...';

  @override
  String get deleteYouCopy => 'আপনার কপি মুছে ফেলুন';

  @override
  String get unMute => 'মিউট বাতিল করুন';

  @override
  String get delete => 'মুছে ফেলুন';

  @override
  String get report => 'রিপোর্ট করুন';

  @override
  String get leaveGroup => 'গ্রুপ ত্যাগ করুন';

  @override
  String get areYouSureToPermitYourCopyThisActionCantUndo =>
      'আপনি কি নিশ্চিত যে আপনি আপনার কপি অনুমতি দিতে চান? এই কার্যটি প্রত্যাহার করা যায় না';

  @override
  String get areYouSureToLeaveThisGroupThisActionCantUndo =>
      'আপনি কি নিশ্চিত যে আপনি এই গ্রুপ ত্যাগ করতে চান? এই কার্যটি প্রত্যাহার করা যায় না';

  @override
  String get leaveGroupAndDeleteYourMessageCopy =>
      'গ্রুপ ত্যাগ করুন এবং আপনার বার্তা কপি মুছে ফেলুন';

  @override
  String get vMessageInfoTrans => 'বার্তা তথ্য';

  @override
  String get updateTitleTo => 'টাইটেল আপডেট করুন';

  @override
  String get updateImage => 'চিত্র আপডেট করুন';

  @override
  String get joinedBy => 'যোগদান করেছেন';

  @override
  String get promotedToAdminBy => 'অ্যাডমিন হিসেবে পোঁচানো হয়েছে';

  @override
  String get dismissedToMemberBy => 'সদস্য হিসেবে বাদ দেওয়া হয়েছে';

  @override
  String get leftTheGroup => 'গ্রুপ ত্যাগ করেছে';

  @override
  String get you => 'আপনি';

  @override
  String get kickedBy => 'কিক দেওয়া হয়েছে';

  @override
  String get groupCreatedBy => 'গ্রুপ তৈরি করেছেন';

  @override
  String get addedYouToNewBroadcast => 'নতুন ব্রডকাস্টে আপনি যোগ দেওয়া হয়েছে';

  @override
  String get download => 'ডাউনলোড করুন';

  @override
  String get copy => 'অনুলিপি';

  @override
  String get info => 'তথ্য';

  @override
  String get share => 'শেয়ার করুন';

  @override
  String get forward => 'ফরোয়ার্ড';

  @override
  String get reply => 'উত্তর দিন';

  @override
  String get reacted => 'Reacted';

  @override
  String get replied => 'Replied';

  @override
  String get deleteFromAll => 'সব থেকে মুছে ফেলুন';

  @override
  String get deleteFromMe => 'আমার থেকে মুছে ফেলুন';

  @override
  String get downloading => 'ডাউনলোড হচ্ছে...';

  @override
  String get fileHasBeenSavedTo => 'ফাইল সফলভাবে সংরক্ষিত হয়েছে';

  @override
  String get online => 'অনলাইন';

  @override
  String get youDontHaveAccess => 'আপনার অ্যাক্সেস নেই';

  @override
  String get replyToYourSelf => 'নিজের উত্তর দিন';

  @override
  String get repliedToYourSelf => 'নিজের উত্তর দেওয়া হয়েছে';

  @override
  String get audioCall => 'অডিও কল';

  @override
  String get ring => 'রিংক';

  @override
  String get canceled => 'বাতিল';

  @override
  String get timeout => 'সময় শেষ';

  @override
  String get rejected => 'প্রত্যাখ্যান';

  @override
  String get finished => 'শেষ';

  @override
  String get inCall => 'কলে';

  @override
  String get sessionEnd => 'সেশন শেষ';

  @override
  String get yesterday => 'গতকাল';

  @override
  String get today => 'আজ';

  @override
  String get textFieldHint => 'একটি বার্তা টাইপ করুন ...';

  @override
  String get files => 'ফাইলগুলি';

  @override
  String get location => 'অবস্থান';

  @override
  String get shareMediaAndLocation => 'মিডিয়া এবং অবস্থান শেয়ার করুন';

  @override
  String get thereIsVideoSizeBiggerThanAllowedSize =>
      'অনুমোদিত সাইজের চেয়ে বড় ভিডিও আছে';

  @override
  String get thereIsFileHasSizeBiggerThanAllowedSize =>
      'অনুমোদিত সাইজের চেয়ে বড় ফাইল আছে';

  @override
  String get makeCall => 'কল করুন';

  @override
  String get areYouWantToMakeVideoCall => 'ভিডিও কল করতে চান?';

  @override
  String get areYouWantToMakeVoiceCall => 'আপনি কি ভয়েস কল করতে চান?';

  @override
  String get vMessagesInfoTrans => 'মেসেজ তথ্য';

  @override
  String get star => 'তারকা';

  @override
  String get minutes => 'মিনিট';

  @override
  String get sendMessage => 'মেসেজ পাঠান';

  @override
  String get deleteUser => 'ব্যবহারকারী মুছে ফেলুন';

  @override
  String get actions => 'ক্রিয়াকলাপগুলি';

  @override
  String get youAreAboutToDeleteThisUserFromYourList =>
      'আপনি আপনার তালিকা থেকে এই ব্যবহারকারীকে মুছে ফেলতে চলেছেন';

  @override
  String get updateBroadcastTitle => 'ব্রডকাস্ট শিরোনাম আপডেট করুন';

  @override
  String get usersAddedSuccessfully =>
      'ব্যবহারকারীগণ সফলভাবে যোগ দেওয়া হয়েছে';

  @override
  String get broadcastSettings => 'ব্রডকাস্ট সেটিংস';

  @override
  String get addParticipants => 'অংশগ্রহণকারী যোগ করুন';

  @override
  String get broadcastParticipants => 'ব্রডকাস্ট অংশগ্রহণকারী';

  @override
  String get updateGroupDescription => 'গ্রুপ বর্ণনা আপডেট করুন';

  @override
  String get updateGroupTitle => 'গ্রুপ শিরোনাম আপডেট করুন';

  @override
  String get groupSettings => 'গ্রুপ সেটিংস';

  @override
  String get description => 'বর্ণনা';

  @override
  String get muteNotifications => 'মিউট বিজ্ঞপ্তি';

  @override
  String get groupParticipants => 'গ্রুপ অংশগ্রহণকারী';

  @override
  String get blockUser => 'ব্যবহারকারী ব্লক করুন';

  @override
  String get areYouSureToBlock =>
      'আপনি কি নিশ্চিত যে ব্যবহারকারীকে ব্লক করতে চান';

  @override
  String get userPage => 'ব্যবহারকারী পৃষ্ঠা';

  @override
  String get starMessage => 'স্টার মেসেজ';

  @override
  String get showMedia => 'মিডিয়া দেখান';

  @override
  String get reportUser => 'ব্যবহারকারীর প্রতি রিপোর্ট';

  @override
  String get groupName => 'গ্রুপ নাম';

  @override
  String get changeSubject => 'বিষয় পরিবর্তন করুন';

  @override
  String get titleIsRequired => 'শিরোনাম প্রয়োজন';

  @override
  String get createBroadcast => 'ব্রডকাস্ট তৈরি করুন';

  @override
  String get broadcastName => 'ব্রডকাস্ট নাম';

  @override
  String get createGroup => 'গ্রুপ তৈরি করুন';

  @override
  String get forgetPassword => 'পাসওয়ার্ড ভুলে গেছেন';

  @override
  String get globalSearch => 'গ্লোবাল অনুসন্ধান';

  @override
  String get dismissesToMember => 'সদস্য ত্যাগ';

  @override
  String get setToAdmin => 'অ্যাডমিন হিসেবে নির্ধারণ করুন';

  @override
  String get kickMember => 'সদস্য বের করুন';

  @override
  String get youAreAboutToDismissesToMember => 'আপনি সদস্য ত্যাগ করতে চলেছেন';

  @override
  String get youAreAboutToKick => 'আপনি সদস্য বের করতে চলেছেন';

  @override
  String get groupMembers => 'গ্রুপ সদস্য';

  @override
  String get tapForPhoto => 'ছবি দেখতে ট্যাপ করুন';

  @override
  String get weHighRecommendToDownloadThisUpdate =>
      'আমরা এই আপডেট ডাউনলোড করার জন্য বেশি সুপারিশ করি';

  @override
  String get newGroup => 'নতুন গ্রুপ';

  @override
  String get newBroadcast => 'নতুন ব্রডকাস্ট';

  @override
  String get starredMessage => 'স্টার মেসেজ';

  @override
  String get settings => 'সেটিংস';

  @override
  String get chats => 'চ্যাট';

  @override
  String get recentUpdates => 'সাম্প্রতিক আপডেট';

  @override
  String get startChat => 'চ্যাট শুরু করুন';

  @override
  String get newUpdateIsAvailable => 'নতুন আপডেট উপলব্ধ';

  @override
  String get emailNotValid => 'ইমেল সঠিক নয়';

  @override
  String get passwordMustHaveValue => 'পাসওয়ার্ড মান রেখে আসতে হবে';

  @override
  String get error => 'ত্রুটি';

  @override
  String get password => 'পাসওয়ার্ড';

  @override
  String get login => 'লগইন';

  @override
  String get needNewAccount => 'নতুন অ্যাকাউন্ট প্রয়োজন?';

  @override
  String get register => 'রেজিস্টার';

  @override
  String get nameMustHaveValue => 'নাম মান রেখে আসতে হবে';

  @override
  String get passwordNotMatch => 'পাসওয়ার্ড মেলে না';

  @override
  String get name => 'নাম';

  @override
  String get email => 'ইমেল';

  @override
  String get confirmPassword => 'পাসওয়ার্ড নিশ্চিত করুন';

  @override
  String get alreadyHaveAnAccount => 'ইতিমধ্যে অ্যাকাউন্ট আছে?';

  @override
  String get logOut => 'লগ আউট';

  @override
  String get back => 'পিছনে';

  @override
  String get sendCodeToMyEmail => 'আমার ইমেলে কোড পাঠান';

  @override
  String get invalidLoginData => 'অবৈধ লগইন তথ্য';

  @override
  String get userEmailNotFound => 'ব্যবহারকারী ইমেল পাওয়া যায়নি';

  @override
  String get yourAccountBlocked => 'আপনার অ্যাকাউন্ট ব্লক করা হয়েছে';

  @override
  String get yourAccountDeleted => 'আপনার অ্যাকাউন্ট মুছে ফেলা হয়েছে';

  @override
  String get userAlreadyRegister => 'ব্যবহারকারী ইতিমধ্যে নিবন্ধিত';

  @override
  String get codeHasBeenExpired => 'কোড মেয়াদ উত্তীর্ণ হয়েছে';

  @override
  String get invalidCode => 'অবৈধ কোড';

  @override
  String get whileAuthCanFindYou =>
      'অথেন্টিকেশন সময়ে আপনি খুঁজে পেতে পারেন না';

  @override
  String get userRegisterStatusNotAcceptedYet =>
      'ব্যবহারকারীর নিবন্ধন স্থিতি এখনও গ্রহণ করা হয়নি';

  @override
  String get deviceHasBeenLogoutFromAllDevices =>
      'সমস্ত ডিভাইস থেকে লগআউট হয়েছে';

  @override
  String get userDeviceSessionEndDeviceDeleted =>
      'ব্যবহারকারী ডিভাইস সেশন শেষ, ডিভাইস মোছা হয়েছে';

  @override
  String get noCodeHasBeenSendToYouToVerifyYourEmail =>
      'আপনার ইমেল যাচাই করতে আপনাকে কোড প্রেরণ করা হয়নি';

  @override
  String get roomAlreadyInCall => 'রুম ইতিমধ্যে কলে';

  @override
  String get peerUserInCallNow => 'ব্যবহারকারী এখন কলে';

  @override
  String get callNotAllowed => 'কল অনুমতি নেই';

  @override
  String get peerUserDeviceOffline => 'পীয়র ব্যবহারকারীর ডিভাইস অফলাইন';

  @override
  String get emailMustBeValid => 'ইমেল সঠিক হতে হবে';

  @override
  String get wait2MinutesToSendMail => 'মেইল পাঠাতে 2 মিনিট অপেক্ষা করুন';

  @override
  String get codeMustEqualToSixNumbers => 'কোডটি ছয়টি সংখ্যার সমান হতে হবে';

  @override
  String get newPasswordMustHaveValue => 'নতুন পাসওয়ার্ড দিতে হবে';

  @override
  String get confirmPasswordMustHaveValue => 'Confirm password must have value';

  @override
  String get congregationsYourAccountHasBeenAccepted =>
      'অভিনন্দন, আপনার অ্যাকাউন্টটি গৃহীত হয়েছে';

  @override
  String get yourAccountIsUnderReview => 'আপনার অ্যাকাউন্টটি পর্যালোচনাধীন';

  @override
  String get waitingList => 'অপেক্ষা তালিকা';

  @override
  String get welcome => 'স্বাগতম';

  @override
  String get retry => 'পুনরায় চেষ্টা করুন';

  @override
  String get deleteMember => 'সদস্য মুছে ফেলুন';

  @override
  String get profile => 'প্রোফাইল';

  @override
  String get broadcastInfo => 'ব্রডকাস্ট তথ্য';

  @override
  String get updateTitle => 'শিরোনাম আপডেট করুন';

  @override
  String get members => 'সদস্য';

  @override
  String get addMembers => 'সদস্য যোগ করুন';

  @override
  String get success => 'সাফল্য';

  @override
  String get media => 'মিডিয়া';

  @override
  String get docs => 'ডকুমেন্ট';

  @override
  String get links => 'লিঙ্ক';

  @override
  String get soon => 'শীঘ্রই';

  @override
  String get unStar => 'তারকা অতীত করুন';

  @override
  String get updateGroupDescriptionWillUpdateAllGroupMembers =>
      'গ্রুপ বর্ণনা আপডেট করলে সমস্ত গ্রুপ সদস্যদের আপডেট হবে';

  @override
  String get updateNickname => 'উপনাম আপডেট করুন';

  @override
  String get groupInfo => 'গ্রুপ তথ্য';

  @override
  String get youNotParticipantInThisGroup => 'আপনি এই গ্রুপে অংশগ্রহণকারী নন';

  @override
  String get search => 'অনুসন্ধান';

  @override
  String get mediaLinksAndDocs => 'মিডিয়া, লিঙ্ক, এবং ডকুমেন্ট';

  @override
  String get starredMessages => 'তারকা বার্তা';

  @override
  String get nickname => 'উপনাম';

  @override
  String get none => 'কোনটি নেই';

  @override
  String get yes => 'হ্যাঁ';

  @override
  String get no => 'না';

  @override
  String get exitGroup => 'গ্রুপ থেকে বের হোন';

  @override
  String get clickToAddGroupDescription => 'গ্রুপ বর্ণনা যোগ করতে ক্লিক করুন';

  @override
  String get unBlockUser => 'ব্যবহারকারী অনব্লক করুন';

  @override
  String get areYouSureToUnBlock => 'আপনি কি নিশ্চিত যে আপনি অনব্লক করতে চান?';

  @override
  String get contactInfo => 'যোগাযোগের তথ্য';

  @override
  String get audio => 'অডিও';

  @override
  String get video => 'ভিডিও';

  @override
  String get hiIamUse => 'হাই, আমি ব্যবহার করি';

  @override
  String get on => 'চালু';

  @override
  String get off => 'বন্ধ';

  @override
  String get unBlock => 'অনব্লক';

  @override
  String get block => 'ব্লক';

  @override
  String get chooseAtLestOneMember => 'অন্তত একজন সদস্য নির্বাচন করুন';

  @override
  String get close => 'বন্ধ করুন';

  @override
  String get next => 'পরবর্তী';

  @override
  String get appMembers => 'অ্যাপ সদস্য';

  @override
  String get create => 'তৈরি করুন';

  @override
  String get upgradeToAdmin => 'অ্যাডমিন হিসেবে উন্নত করুন';

  @override
  String get update => 'আপডেট';

  @override
  String get deleteChat => 'চ্যাট মুছুন';

  @override
  String get clearChat => 'চ্যাট স্পষ্ট করুন';

  @override
  String get showHistory => 'ইতিহাস দেখান';

  @override
  String get groupIcon => 'গ্রুপ আইকন';

  @override
  String get tapToSelectAnIcon => 'একটি আইকন নির্বাচন করতে ট্যাপ করুন';

  @override
  String get groupDescription => 'গ্রুপ বর্ণনা';

  @override
  String get more => 'আরও';

  @override
  String get messageInfo => 'বার্তা তথ্য';

  @override
  String get successfullyDownloadedIn => 'সাফল্যের সাথে ডাউনলোড হয়েছে';

  @override
  String get delivered => 'ডেলিভার হয়েছে';

  @override
  String get read => 'পঠিত';

  @override
  String get orLoginWith => 'বা লগ ইন করুন';

  @override
  String get resetPassword => 'পাসওয়ার্ড রিসেট করুন';

  @override
  String get otpCode => 'ওটিপি কোড';

  @override
  String get newPassword => 'নতুন পাসওয়ার্ড';

  @override
  String get areYouSure => 'আপনি কি নিশ্চিত?';

  @override
  String get broadcastMembers => 'ব্রডকাস্ট সদস্য';

  @override
  String get phone => 'ফোন';

  @override
  String get users => 'ব্যবহারকারী';

  @override
  String get calls => 'কল';

  @override
  String get yourAreAboutToLogoutFromThisAccount =>
      'আপনি এই অ্যাকাউন্ট থেকে লগআউট হতে চলেছেন';

  @override
  String get noUpdatesAvailableNow => 'এখন কোন আপডেট পাওয়া যায় না';

  @override
  String get dataPrivacy => 'ডেটা গোপনীয়তা';

  @override
  String get allDataHasBeenBackupYouDontNeedToManageSaveTheDataByYourself =>
      'সমস্ত ডেটা ব্যাকআপ হয়েছে, আপনাকে স্বয়ং ডেটা সংরক্ষণ করতে দরকার নেই! আপনি যদি লগআউট করে আবার লগইন করেন, তবে আপনি সমস্ত চ্যাটগুলি দেখতে পাবেন, এটি ওয়েব সংস্করণের জন্য সময় সামান্য';

  @override
  String get account => 'অ্যাকাউন্ট';

  @override
  String get linkedDevices => 'লিঙ্কড ডিভাইস';

  @override
  String get storageAndData => 'স্টোরেজ এবং ডেটা';

  @override
  String get tellAFriend => 'একটি বন্ধুকে বলুন';

  @override
  String get help => 'সাহায্য';

  @override
  String get blockedUsers => 'ব্লক করা ব্যবহারকারী';

  @override
  String get inAppAlerts => 'অ্যাপ সতর্কবাণী';

  @override
  String get language => 'ভাষা';

  @override
  String get adminNotification => 'অ্যাডমিন বিজ্ঞপ্তি';

  @override
  String get checkForUpdates => 'আপডেট চেক করুন';

  @override
  String get linkByQrCode => 'কিউআর কোড দ্বারা লিঙ্ক করুন';

  @override
  String get deviceStatus => 'ডিভাইস স্থিতি';

  @override
  String get desktopAndOtherDevices => 'ডেস্কটপ এবং অন্যান্য ডিভাইস';

  @override
  String get linkADeviceSoon => 'শীঘ্রই একটি ডিভাইস লিঙ্ক করুন (শীঘ্রই)';

  @override
  String get lastActiveFrom => 'সর্বশেষ সক্রিয় হওয়া থেকে';

  @override
  String get tapADeviceToEditOrLogOut =>
      'সম্পাদনা করতে বা লগআউট করতে একটি ডিভাইসে ট্যাপ করুন।';

  @override
  String get contactUs => 'যোগাযোগ করুন';

  @override
  String get supportChatSoon => 'সমর্থন চ্যাট (শীঘ্রই)';

  @override
  String get updateYourName => 'আপনার নাম আপডেট করুন';

  @override
  String get updateYourBio => 'আপনার বায়ো আপডেট করুন';

  @override
  String get edit => 'সম্পাদনা';

  @override
  String get about => 'সম্পর্কিত';

  @override
  String get oldPassword => 'পুরানো পাসওয়ার্ড';

  @override
  String get deleteMyAccount => 'আমার অ্যাকাউন্ট মুছুন';

  @override
  String get passwordHasBeenChanged => 'পাসওয়ার্ড পরিবর্তন হয়েছে';

  @override
  String get logoutFromAllDevices => 'সমস্ত ডিভাইস থেকে লগআউট করতে?';

  @override
  String get updateYourPassword => 'আপনার পাসওয়ার্ড আপডেট করুন';

  @override
  String get enterNameAndAddOptionalProfilePicture =>
      'আপনার নাম লিখুন এবং ঐচ্ছিক প্রোফাইল ছবি যোগ করুন';

  @override
  String get privacyPolicy => 'গোপনীয়তা নীতি';

  @override
  String get chat => 'চ্যাট';

  @override
  String get send => 'প্রেরণ করুন';

  @override
  String get reportHasBeenSubmitted => 'আপনার রিপোর্ট জমা দেওয়া হয়েছে';

  @override
  String get offline => 'অফলাইন';

  @override
  String get harassmentOrBullyingDescription =>
      'হেরাসমেন্ট অথবা বুলিং: এই অপশনটি ব্যবহারকারীদের এমন ব্যক্তিদের সম্পর্কে রিপোর্ট জমা দেওয়ার জন্য অনুমতি দেয় যারা তাদের বা অন্যকে হেরাসমেন্ট, হুমকি, বা অন্যান্য বুলিং রকমের বিরুদ্ধে বার্তা পাঠাচ্ছে।';

  @override
  String get spamOrScamDescription =>
      'স্প্যাম অথবা প্রতারণা: এই অপশনটি ব্যবহারকারীদের স্প্যাম বার্তা, অপ্রাপ্ত বিজ্ঞাপন, বা অন্যান্য স্প্যাম প্রেরণা বা প্রতারণা প্রেরণার ব্যবস্থা করতে দেয়।';

  @override
  String get areYouSureToReportUserToAdmin =>
      'আপনি কি নিশ্চিত যে আপনি এই ব্যবহারকারীকে অ্যাডমিনের কাছে রিপোর্ট জমা দেওয়ার দ্বারা সহমত?';

  @override
  String get groupWith => 'গ্রুপের সাথে';

  @override
  String get inappropriateContentDescription =>
      'অযোগ্য বিষয়বস্তু: ব্যবহারকারীরা এই অপশনটি ব্যবহার করে সেক্সুয়ালি স্পষ্ট বাণিজ্যিক সামগ্রী, ঘৃণা ভাষণ, অথবা অন্যান্য সামুদ্রিক মানবাধিকার লঙ্ঘনের মত কোনও বিষয়বস্তু রিপোর্ট করতে পারে।';

  @override
  String get otherCategoryDescription =>
      'অন্যান্য: এই সার্বজনীন বিভাগটি উপরের বিভাগে সুস্থতায় পূর্ণ হওয়া যায় না সে ধরনের লঙ্ঘনের জন্য ব্যবহার করা যেতে পারে। ব্যবহারকারীদের অতিরিক্ত বিশদ প্রদান করতে ব্যবহারকারীদের জন্য একটি পাঠ বক্স যোগ করা সাহায্যকর।';

  @override
  String get explainWhatHappens => 'এখানে ঘটনা সারাংশ করুন';

  @override
  String get loginAgain => 'আবার লগইন করুন!';

  @override
  String get yourSessionIsEndedPleaseLoginAgain =>
      'আপনার সেশন শেষ হয়েছে, দয়া করে আবার লগইন করুন!';

  @override
  String get aboutToBlockUserWithConsequences =>
      'আপনি এই ব্যবহারকারীকে ব্লক করতে চলেছেন। আপনি তাকে চ্যাট পাঠাতে পারবেন না এবং তাকে গ্রুপ বা ব্রডকাস্টে যোগ করতে পারবেন না!';

  @override
  String get youAreAboutToDeleteYourAccountYourAccountWillNotAppearAgainInUsersList =>
      'আপনি আপনার অ্যাকাউন্ট মুছতে চলেছেন, আপনার অ্যাকাউন্ট আবার ব্যবহারকারীর তালিকায় অবস্থান করবে না';

  @override
  String get admin => 'অ্যাডমিন';

  @override
  String get member => 'সদস্য';

  @override
  String get creator => 'সৃষ্টিকর্তা';

  @override
  String get currentDevice => 'বর্তমান ডিভাইস';

  @override
  String get visits => 'দর্শন';

  @override
  String get chooseRoom => 'কক্ষ চয়ন করুন';

  @override
  String get deleteThisDeviceDesc => 'এই ডিভাইসটি মুছলেই অবসান্ন লগআউট হবে';

  @override
  String get youAreAboutToUpgradeToAdmin =>
      'আপনি অ্যাডমিন হিসেবে উন্নত হতে চলেছেন';

  @override
  String get microphonePermissionMustBeAccepted =>
      'Microphone permission must be accepted';

  @override
  String get microphoneAndCameraPermissionMustBeAccepted =>
      'Microphone and camera permission must be accepted';

  @override
  String get loginNowAllowedNowPleaseTryAgainLater =>
      'দুঃখিত, এখন লগইন অনুমোদিত নয়। দয়া করে পরে আবার চেষ্টা করুন।';

  @override
  String get dashboard => 'ড্যাশবোর্ড';

  @override
  String get notification => 'বিজ্ঞপ্তি';

  @override
  String get total => 'মোট';

  @override
  String get blocked => 'ব্লক করা হয়েছে';

  @override
  String get deleted => 'মোছা হয়েছে';

  @override
  String get accepted => 'গ্রহণ করা হয়েছে';

  @override
  String get notAccepted => 'গ্রহণ করা হয়নি';

  @override
  String get web => 'ওয়েব';

  @override
  String get android => 'অ্যান্ড্রয়েড';

  @override
  String get macOs => 'macOS';

  @override
  String get windows => 'উইন্ডোজ';

  @override
  String get other => 'অন্যান্য';

  @override
  String get totalVisits => 'মোট দর্শন';

  @override
  String get totalMessages => 'মোট মেসেজ';

  @override
  String get textMessages => 'টেক্সট মেসেজ';

  @override
  String get imageMessages => 'চিত্র মেসেজ';

  @override
  String get videoMessages => 'ভিডিও মেসেজ';

  @override
  String get voiceMessages => 'ভয়েস মেসেজ';

  @override
  String get fileMessages => 'ফাইল মেসেজ';

  @override
  String get infoMessages => 'তথ্য মেসেজ';

  @override
  String get voiceCallMessages => 'ভয়েস কল মেসেজ';

  @override
  String get videoCallMessages => 'ভিডিও কল মেসেজ';

  @override
  String get locationMessages => 'অবস্থান মেসেজ';

  @override
  String get directChat => 'সরাসরি চ্যাট';

  @override
  String get group => 'গ্রুপ';

  @override
  String get broadcast => 'ব্রডকাস্ট';

  @override
  String get messageCounter => 'মেসেজ গণনা';

  @override
  String get roomCounter => 'রুম গণনা';

  @override
  String get countries => 'দেশ';

  @override
  String get devices => 'ডিভাইস';

  @override
  String get notificationTitle => 'বিজ্ঞপ্তি শিরোনাম';

  @override
  String get notificationDescription => 'বিজ্ঞপ্তি বিবরণ';

  @override
  String get notificationsPage => 'বিজ্ঞপ্তি পৃষ্ঠা';

  @override
  String get updateFeedBackEmail => 'ফিডব্যাক ইমেইল আপডেট করুন';

  @override
  String get setMaxMessageForwardAndShare =>
      'ম্যাসেজ ফরওয়ার্ড এবং শেয়ারের সর্বোচ্চ সীমা সেট করুন';

  @override
  String get setNewPrivacyPolicyUrl => 'নতুন গোপনীয়তা নীতি URL সেট করুন';

  @override
  String get forgetPasswordExpireTime => 'পাসওয়ার্ড ভুলে যাওয়ার মেয়াদ';

  @override
  String get callTimeoutInSeconds => 'কল সময় আউট (সেকেন্ডে)';

  @override
  String get setMaxGroupMembers => 'গ্রুপ সদস্যের সর্বোচ্চ সংখ্যা সেট করুন';

  @override
  String get setMaxBroadcastMembers =>
      'ব্রডকাস্ট সদস্যের সর্বোচ্চ সংখ্যা সেট করুন';

  @override
  String get allowCalls => 'কল অনুমোদন দিন';

  @override
  String get ifThisOptionEnabledTheVideoAndVoiceCallWillBeAllowed =>
      'এই অপশন সক্ষম হলে ভিডিও এবং ভয়েস কল অনুমোদিত হবে';

  @override
  String get allowAds => 'বিজ্ঞাপন অনুমোদন দিন';

  @override
  String get allowMobileLogin => 'মোবাইল লগইন অনুমোদন দিন';

  @override
  String get allowWebLogin => 'ওয়েব লগইন অনুমোদন দিন';

  @override
  String get messages => 'মেসেজ';

  @override
  String get appleStoreAppUrl => 'আপেল স্টোর অ্যাপ URL';

  @override
  String get googlePlayAppUrl => 'গুগল প্লে স্টোর অ্যাপ URL';

  @override
  String get privacyUrl => 'গোপনীয়তা URL';

  @override
  String get feedBackEmail => 'ফিডব্যাক ইমেইল';

  @override
  String get ifThisOptionDisabledTheSendChatFilesImageVideosAndLocationWillBeBlocked =>
      'এই অপশন নিষ্ক্রিয় হলে চ্যাট ফাইল, ছবি, ভিডিও এবং অবস্থান প্রেরণ বন্ধ হবে';

  @override
  String get allowSendMedia => 'মিডিয়া প্রেরণ অনুমোদন দিন';

  @override
  String get ifThisOptionDisabledTheCreateChatBroadcastWillBeBlocked =>
      'এই অপশন নিষ্ক্রিয় হলে চ্যাট ব্রডকাস্ট তৈরি করা বন্ধ হবে';

  @override
  String get allowCreateBroadcast => 'ব্রডকাস্ট তৈরি করার অনুমোদন দিন';

  @override
  String get ifThisOptionDisabledTheCreateChatGroupsWillBeBlocked =>
      'এই অপশন নিষ্ক্রিয় হলে চ্যাট গ্রুপ তৈরি করা বন্ধ হবে';

  @override
  String get allowCreateGroups => 'গ্রুপ তৈরি করার অনুমোদন দিন';

  @override
  String get ifThisOptionDisabledTheDesktopLoginOrRegisterWindowsMacWillBeBlocked =>
      'এই অপশন নিষ্ক্রিয় হলে ডেস্কটপ লগইন বা রেজিস্টার (Windows এবং macOS) বন্ধ হবে';

  @override
  String get allowDesktopLogin => 'ডেস্কটপ লগইন অনুমোদন দিন';

  @override
  String get ifThisOptionDisabledTheWebLoginOrRegisterWillBeBlocked =>
      'এই অপশন নিষ্ক্রিয় হলে ওয়েব লগইন বা রেজিস্টার বন্ধ হবে';

  @override
  String
      get ifThisOptionDisabledTheMobileLoginOrRegisterWillBeBlockedOnAndroidIosOnly =>
          'এই অপশন সক্ষম হলে Google Ads ব্যানার চ্যাটগুলিতে দেখা যাবে';

  @override
  String get ifThisOptionEnabledTheGoogleAdsBannerWillAppearInChats =>
      'If this option is enabled, the Google Ads banner will appear in chats.';

  @override
  String get userProfile => 'ব্যবহারকারী প্রোফাইল';

  @override
  String get userInfo => 'ব্যবহারকারী তথ্য';

  @override
  String get fullName => 'পুরো নাম';

  @override
  String get bio => 'জীবনী';

  @override
  String get noBio => 'কোনো জীবনী নেই';

  @override
  String get verifiedAt => 'যাচাই করা হয়েছে';

  @override
  String get country => 'দেশ';

  @override
  String get registerStatus => 'রেজিস্টার স্ট্যাটাস';

  @override
  String get registerMethod => 'রেজিস্টার মেথড';

  @override
  String get banTo => 'ব্যান করা হয়েছে';

  @override
  String get deletedAt => 'মোছা হয়েছে';

  @override
  String get createdAt => 'তৈরি হয়েছে';

  @override
  String get updatedAt => 'আপডেট হয়েছে';

  @override
  String get reports => 'রিপোর্ট';

  @override
  String get clickToSeeAllUserDevicesDetails =>
      'সমস্ত ব্যবহারকারী ডিভাইস বিশদ দেখতে ক্লিক করুন';

  @override
  String get allDeletedMessages => 'সমস্ত মোছা মেসেজ';

  @override
  String get voiceCallMessage => 'ভয়েস কল মেসেজ';

  @override
  String get totalRooms => 'মোট রুম';

  @override
  String get directRooms => 'সরাসরি রুম';

  @override
  String get userAction => 'ব্যবহারকারী ক্রিয়া';

  @override
  String get status => 'স্থিতি';

  @override
  String get joinedAt => 'যোগদান করেছেন';

  @override
  String get saveLogin => 'লগইন সংরক্ষণ করুন';

  @override
  String get passwordIsRequired => 'পাসওয়ার্ড প্রয়োজন';

  @override
  String get verified => 'যাচাইকৃত';

  @override
  String get pending => 'মুলতবি';

  @override
  String get ios => 'iOS';

  @override
  String get descriptionIsRequired => 'বর্ণনা প্রয়োজন';

  @override
  String get seconds => 'সেকেন্ড';

  @override
  String get clickToSeeAllUserInformations =>
      'সমস্ত ব্যবহারকারী তথ্য দেখতে ক্লিক করুন';

  @override
  String get clickToSeeAllUserCountries =>
      'সমস্ত ব্যবহারকারী দেশের তথ্য দেখতে ক্লিক করুন';

  @override
  String get clickToSeeAllUserMessagesDetails =>
      'সমস্ত ব্যবহারকারী মেসেজের বিশদ দেখতে ক্লিক করুন';

  @override
  String get clickToSeeAllUserRoomsDetails =>
      'সমস্ত ব্যবহারকারী রুমের বিশদ দেখতে ক্লিক করুন';

  @override
  String get clickToSeeAllUserReports =>
      'সমস্ত ব্যবহারকারী রিপোর্ট দেখতে ক্লিক করুন';

  @override
  String get banAt => 'ব্যান করা হয়েছে';

  @override
  String get nowYouLoginAsReadOnlyAdminAllEditYouDoneWillNotAppliedDueToThisIsTestVersion =>
      'এখন আপনি কেবল পঠনের মোড়ে অ্যাডমিন হিসেবে লগইন করেছেন। এই টেস্ট সংস্করণে আপনি যে কোন সম্পাদনা করলে প্রযোজ্য হবে না।';

  @override
  String get createStory => 'গল্প তৈরি করুন';

  @override
  String get writeACaption => 'ক্যাপশন লিখুন...';

  @override
  String get storyCreatedSuccessfully => 'গল্প সফলভাবে তৈরি করা হয়েছে';

  @override
  String get stories => 'গল্প';

  @override
  String get clear => 'মুছে ফেলুন';

  @override
  String get clearCallsConfirm => 'কল পরিষ্কার করার নিশ্চিতকরণ';

  @override
  String get chooseHowAutomaticDownloadWorks =>
      'কিভাবে স্বয়ংক্রিয় ডাউনলোড কাজ করে তা চয়ন করুন';

  @override
  String get whenUsingMobileData => 'মোবাইল ডেটা ব্যবহার করার সময়';

  @override
  String get whenUsingWifi => 'Wi-Fi ব্যবহার করার সময়';

  @override
  String get image => 'চিত্র';

  @override
  String get myPrivacy => 'আমার গোপনীয়তা';

  @override
  String get createTextStory => 'টেক্সট গল্প তৈরি করুন';

  @override
  String get createMediaStory => 'মিডিয়া গল্প তৈরি করুন';

  @override
  String get camera => 'ক্যামেরা';

  @override
  String get gallery => 'গ্যালারি';

  @override
  String get recentUpdate => 'সাম্প্রতিক আপডেট';

  @override
  String get viewedUpdates => 'Viewed updates';

  @override
  String get addNewStory => 'নতুন গল্প যোগ করুন';

  @override
  String get updateYourProfile => 'আপনার প্রোফাইল আপডেট করুন';

  @override
  String get configureYourAccountPrivacy =>
      'আপনার অ্যাকাউন্ট গোপনীয়তা কনফিগার করুন';

  @override
  String get youInPublicSearch => 'আপনি পাবলিক অনুসন্ধানে';

  @override
  String get yourProfileAppearsInPublicSearchAndAddingForGroups =>
      'আপনার প্রোফাইল পাবলিক অনুসন্ধানে এবং গ্রুপগুলিতে যোগ করার জন্য উপস্থিত হয়';

  @override
  String get yourLastSeen => 'আপনার শেষ দেখা';

  @override
  String get yourLastSeenInChats => 'চ্যাটে আপনার শেষ দেখা';

  @override
  String get startNewChatWithYou => 'আপনার সাথে নতুন চ্যাট শুরু করুন';

  @override
  String get yourStory => 'আপনার গল্প';

  @override
  String get forRequest => 'অনুরোধের জন্য';

  @override
  String get public => 'सार्वजनिक';

  @override
  String get createYourStory => 'আপনার গল্প তৈরি করুন';

  @override
  String get shareYourStatus => 'আপনার অবস্থা শেয়ার করুন';

  @override
  String get oneSeenMessage => 'একটি দেখা বার্তা';

  @override
  String get messageHasBeenViewed => 'संदेश देखा गया है';

  @override
  String get clickToSee => 'देखने के लिए क्लिक करें';

  @override
  String get images => 'চিত্র';

  @override
  String get switchAccount => 'Switch Account';

  @override
  String get addAccount => 'Add Account';

  @override
  String get manageYourAccounts => 'Manage your accounts';

  @override
  String get addAnotherAccount => 'Add another account';

  @override
  String get selectAccountToSwitchTo => 'Select account to switch to';

  @override
  String get errorLoadingAccounts => 'Error loading accounts';

  @override
  String get noAccountsFound => 'No accounts found';

  @override
  String get active => 'Active';

  @override
  String get removeAccount => 'Remove Account';

  @override
  String get areYouSureRemoveAccount =>
      'Are you sure you want to remove this account?';

  @override
  String get accountRemoved => 'Account removed';

  @override
  String get errorRemovingAccount => 'Error removing account';

  @override
  String switchedToAccount(Object name) {
    return 'Switched to $name';
  }

  @override
  String get errorSwitchingAccount => 'Error switching account';

  @override
  String get accountAddedSuccessfully => 'Account added successfully';

  @override
  String get addProfilePicture => 'Add Profile Picture';

  @override
  String get addProfilePictureSubtitle =>
      'Add a profile picture to help others recognize you';

  @override
  String get pleaseSelectProfilePicture => 'Please select a profile picture';

  @override
  String get uploading => 'Uploading...';

  @override
  String get continueText => 'Continue';

  @override
  String get profilePictureRequired =>
      'A profile picture is required to continue. Please select an image to upload.';
}
